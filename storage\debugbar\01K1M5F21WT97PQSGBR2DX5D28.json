{"__meta": {"id": "01K1M5F21WT97PQSGBR2DX5D28", "datetime": "2025-08-02 01:13:43", "utime": **********.741669, "method": "POST", "uri": "/public-bookings", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 3, "messages": [{"message": "[01:13:40] LOG.info: Starting webhook dispatch for action: booking.booking_form_submitted {\n    \"timestamp\": \"2025-08-02T01:13:40.225596Z\",\n    \"source\": \"crm_webhook_system\",\n    \"action\": \"booking.booking_form_submitted\",\n    \"user_id\": 74,\n    \"entity_type\": \"Booking\",\n    \"entity_id\": 6,\n    \"status\": \"dispatching\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.254282, "xdebug_link": null, "collector": "log"}, {"message": "[01:13:43] LOG.error: <PERSON>ho<PERSON> failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2036 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {\n    \"timestamp\": \"2025-08-02T01:13:43.728972Z\",\n    \"source\": \"crm_webhook_system\",\n    \"action\": \"booking.booking_form_submitted\",\n    \"module_name\": \"OMX FLOW\",\n    \"webhook_url\": \"http:\\/\\/127.0.0.1:2000\\/external-crm\\/webhook\",\n    \"status\": \"failed\",\n    \"status_code\": null,\n    \"response_time_ms\": 3470,\n    \"user_id\": 74,\n    \"entity_id\": 6,\n    \"entity_type\": null,\n    \"request_payload\": {\n        \"action\": \"booking.booking_form_submitted\",\n        \"timestamp\": \"2025-08-02T01:13:40.258582Z\",\n        \"data\": {\n            \"event_id\": 3,\n            \"name\": \"<PERSON><PERSON>\",\n            \"email\": \"<EMAIL>\",\n            \"phone\": \"06295298485\",\n            \"date\": \"2025-08-09\",\n            \"time\": \"13:30\",\n            \"selected_location\": {\n                \"type\": \"zoom\",\n                \"value\": \"https:\\/\\/dewfrreafreafewafewf\",\n                \"display\": \"Zoom\"\n            },\n            \"custom_fields\": [],\n            \"custom_fields_value\": [],\n            \"payment_amount\": \"0.00\",\n            \"payment_status\": \"not_required\",\n            \"status\": \"scheduled\",\n            \"updated_at\": \"2025-08-02T01:13:40.000000Z\",\n            \"created_at\": \"2025-08-02T01:13:40.000000Z\",\n            \"id\": 6,\n            \"form_data\": {\n                \"name\": \"Mintu Barman\",\n                \"email\": \"<EMAIL>\",\n                \"phone\": \"06295298485\",\n                \"date\": \"2025-08-09\",\n                \"time\": \"13:30\",\n                \"selected_location\": {\n                    \"type\": \"zoom\",\n                    \"value\": \"https:\\/\\/dewfrreafreafewafewf\",\n                    \"display\": \"Zoom\"\n                },\n                \"custom_fields\": [],\n                \"custom_fields_value\": [],\n                \"booking_type\": \"public\"\n            },\n            \"triggered_by\": {\n                \"user_id\": 74,\n                \"email\": \"<EMAIL>\",\n                \"name\": \"deep sha\",\n                \"type\": \"company\"\n            }\n        },\n        \"user_id\": 74,\n        \"source\": {\n            \"system\": \"krishna\",\n            \"version\": \"1.0\",\n            \"url\": \"http:\\/\\/localhost:8000\"\n        }\n    },\n    \"error_message\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2036 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/external-crm\\/webhook\",\n    \"response_body\": null\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.729931, "xdebug_link": null, "collector": "log"}, {"message": "[01:13:43] LOG.warning: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 1 {\n    \"timestamp\": \"2025-08-02T01:13:43.730169Z\",\n    \"source\": \"crm_webhook_system\",\n    \"action\": \"booking.booking_form_submitted\",\n    \"user_id\": 74,\n    \"status\": \"completed\",\n    \"total_modules\": 1,\n    \"successful_modules\": 0,\n    \"failed_modules\": 1,\n    \"modules\": [\n        \"OMX FLOW\"\n    ],\n    \"results\": {\n        \"OMX FLOW\": {\n            \"success\": false,\n            \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2036 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/external-crm\\/webhook\",\n            \"integration\": \"OMX FLOW\"\n        }\n    }\n}", "message_html": null, "is_string": false, "label": "warning", "time": **********.730421, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 4, "start": 1754097219.518298, "end": **********.741695, "duration": 4.2233970165252686, "duration_str": "4.22s", "measures": [{"label": "Booting", "start": 1754097219.518298, "relative_start": 0, "end": **********.056594, "relative_end": **********.056594, "duration": 0.****************, "duration_str": "538ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.056605, "relative_start": 0.****************, "end": **********.741698, "relative_end": 3.0994415283203125e-06, "duration": 3.****************, "duration_str": "3.69s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.065032, "relative_start": 0.***************, "end": **********.075095, "relative_end": **********.075095, "duration": 0.010062932968139648, "duration_str": "10.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.73798, "relative_start": 4.***************, "end": **********.738525, "relative_end": **********.738525, "duration": 0.0005450248718261719, "duration_str": "545μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST public-bookings", "middleware": "web", "controller": "App\\Http\\Controllers\\BookingController@publicStore<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FBookingController.php&line=130\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "public-bookings.store", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FBookingController.php&line=130\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/BookingController.php:130-215</a>"}, "queries": {"count": 6, "nb_statements": 6, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02634, "accumulated_duration_str": "26.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `calendar_events` where `id` = 3", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 984}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 955}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 686}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 481}], "start": **********.148557, "duration": 0.01866, "duration_str": "18.66ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "new_omx_saas", "explain": null, "start_percent": 0, "width_percent": 70.843}, {"sql": "select * from `calendar_events` where `calendar_events`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/BookingController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\BookingController.php", "line": 147}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.173326, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "BookingController.php:147", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/BookingController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\BookingController.php", "line": 147}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FBookingController.php&line=147", "ajax": false, "filename": "BookingController.php", "line": "147"}, "connection": "new_omx_saas", "explain": null, "start_percent": 70.843, "width_percent": 3.948}, {"sql": "insert into `bookings` (`event_id`, `name`, `email`, `phone`, `date`, `time`, `selected_location`, `custom_fields`, `custom_fields_value`, `payment_amount`, `payment_status`, `status`, `updated_at`, `created_at`) values (3, '<PERSON><PERSON>', '<EMAIL>', '06295298485', '2025-08-09', '13:30', '{\\\"type\\\":\\\"zoom\\\",\\\"value\\\":\\\"https:\\\\/\\\\/dewfrreafreafewafewf\\\",\\\"display\\\":\\\"Zoom\\\"}', '[]', '[]', 0, 'not_required', 'scheduled', '2025-08-02 01:13:40', '2025-08-02 01:13:40')", "type": "query", "params": [], "bindings": [3, "<PERSON><PERSON>", "<EMAIL>", "06295298485", "2025-08-09", "13:30", "{\"type\":\"zoom\",\"value\":\"https:\\/\\/dewfrreafreafewafewf\",\"display\":\"Zoom\"}", "[]", "[]", 0, "not_required", "scheduled", "2025-08-02 01:13:40", "2025-08-02 01:13:40"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/BookingController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\BookingController.php", "line": 150}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.180964, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "BookingController.php:150", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/BookingController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\BookingController.php", "line": 150}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FBookingController.php&line=150", "ajax": false, "filename": "BookingController.php", "line": "150"}, "connection": "new_omx_saas", "explain": null, "start_percent": 74.791, "width_percent": 14.92}, {"sql": "select * from `users` where `id` = 74 limit 1", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 21, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 54}], "start": **********.213843, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "new_omx_saas", "explain": null, "start_percent": 89.711, "width_percent": 3.834}, {"sql": "select * from `users` where `users`.`id` = 74 limit 1", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 411}, {"index": 21, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 22, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/BookingController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\BookingController.php", "line": 179}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.222376, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "CrmWebhookDispatcher.php:411", "source": {"index": 20, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FServices%2FCrmWebhookDispatcher.php&line=411", "ajax": false, "filename": "CrmWebhookDispatcher.php", "line": "411"}, "connection": "new_omx_saas", "explain": null, "start_percent": 93.546, "width_percent": 3.872}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/ModuleWebhookService.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Services\\ModuleWebhookService.php", "line": 61}, {"index": 16, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 69}, {"index": 17, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 306}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/BookingController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\BookingController.php", "line": 179}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.255513, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "ModuleWebhookService.php:61", "source": {"index": 15, "namespace": null, "name": "app/Services/ModuleWebhookService.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Services\\ModuleWebhookService.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FServices%2FModuleWebhookService.php&line=61", "ajax": false, "filename": "ModuleWebhookService.php", "line": "61"}, "connection": "new_omx_saas", "explain": null, "start_percent": 97.418, "width_percent": 2.582}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\CalendarEvent": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FCalendarEvent.php&line=1", "ajax": false, "filename": "CalendarEvent.php", "line": "?"}}, "App\\Models\\Booking": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FBooking.php&line=1", "ajax": false, "filename": "Booking.php", "line": "?"}}, "App\\Models\\ModuleIntegration": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FModuleIntegration.php&line=1", "ajax": false, "filename": "ModuleIntegration.php", "line": "?"}}}, "count": 5, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 4, "created": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWSxIFjNu1s5OpxXmNdhn5W8ul114PqK4OYo41zx", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/calendar/all\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "74", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/public-bookings", "action_name": "public-bookings.store", "controller_action": "App\\Http\\Controllers\\BookingController@publicStore", "uri": "POST public-bookings", "controller": "App\\Http\\Controllers\\BookingController@publicStore<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FBookingController.php&line=130\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FBookingController.php&line=130\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/BookingController.php:130-215</a>", "middleware": "web", "duration": "4.23s", "peak_memory": "56MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1567305951 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1567305951\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-58012873 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>event_id</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Mintu Barman</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"25 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"11 characters\">06295298485</span>\"\n  \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-09</span>\"\n  \"<span class=sf-dump-key>time</span>\" => \"<span class=sf-dump-str title=\"5 characters\">13:30</span>\"\n  \"<span class=sf-dump-key>selected_location</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">zoom</span>\"\n    \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">https://dewfrreafreafewafewf</span>\"\n    \"<span class=sf-dump-key>display</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Zoom</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>timezone</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Asia/Calcutta</span>\"\n  \"<span class=sf-dump-key>custom_fields</span>\" => []\n  \"<span class=sf-dump-key>custom_fields_value</span>\" => []\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWSxIFjNu1s5OpxXmNdhn5W8ul114PqK4OYo41zx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-58012873\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1817809157 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">344</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWSxIFjNu1s5OpxXmNdhn5W8ul114PqK4OYo41zx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/calendar-events/3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjZVREVBbC9XSnIwcnNtSE5nWGdkbWc9PSIsInZhbHVlIjoiSUZqRHlsTHl1NWFFNVYzbTdnRFhIdW5DUjY0RVFjV01mOXFNSnU5aFpBcnpsV3BWT21OZUwyM0pVMWxNT2VYZUlEa1A2WHU4cDYveHF6NVF3N1ZnV3lNY2pLM1BLUkJOMlZJZXJ0SUxnTjJJSC9BYmNKbHp1V1FlWGZNL3IweVM4Mi9jcUw4U3ZBYnZoY2JaRTkzbGo2L2pSUlVxMGk3b0wxUWZ2d2JkSUlwTDZnbUFsc3BRSGh2ZVRnZFBiV1o5aTlZcXlncElWVDA2VXdhei8wVEE2bXQzWkpUbSthTDI4eDNOaGtja0ZMVT0iLCJtYWMiOiJmYzk1YjdhMmFhYmZjYTM1ZjAxZWM4NTA5NWEyNWRmYjE1OWUxYjlkMTQ1NzUzMTY5NDM2YTk0Y2EyMDc5ZmI3IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6Im9La0FzZlE2YmtRMDlMT1VjMVRIcEE9PSIsInZhbHVlIjoiMUkvTE9PZ1hSUTUxUHBXcWtzZXlpWHQyenJmNEE4bkg1cDZMenFzbmZQUGkrbnNZWGRib2JpS2RZZVNGbWJRaXk1aFBUcTZ0bXlLMHkvWFQxL1lJK0RER2RjU0QrRFM5VE9uc211SlNnM1JKNllwTm9Nazhqbkk2MllKbzB1WkhROUQvYUhGS2tjbGhmb2ptdElSL2xhZlU2V0lNOGswVGJ6ZGhNM2JGbXNEWUVQUWZNYzdkcGd6bFdyL2ViU0JBcDJRb0R0NGRHeWtrUVEvV2N1NDZCZHBSdFJZSkR4Mmw4M01WVVQ4V0Y2UmZ2WGVTeHlhNWw3b3ZNam1XV3lEQU84Um0wQlZUQkhnbWlycVJnZVV4UXM5alVzbXpDV2NhcER6MjVRYXdRRmVlQlh1dncyaGV6NlZQZkJJSUhOVTdjbm9ZM2MyTm9PbFNVRmFLcVdXOGN6ZXBHeVppQ2JNZy9pKzhSVWkwRnRmZTk0dXFlckloaThzajk3b3B5bUEyT2tWUU1OR3NsK214czZCZkxTaWNsTWo0eTAzWXpFR0YyRE9aenFpbGtySFFyM1NzK2x0bDVvRGlmbnRObkxodlpPeDhsTVVWSFoyUG1MWEdmd08zMSsrbWZCUW01TjNFd0hBVk12b3hUMk5RdzNVSUN0V0pvQVdWTGtnaWRaZE0iLCJtYWMiOiJjOGNkMDY1OWZjMjQxNjU3MWQ4YmI1NDdjNGQzY2ZjMTE0Nzc2ZTg2NWYxNWYzNzc2YThmYjVmODg4YTg2ODg4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImpLZ2FWWTJMNWJjR245ZytidXhBZkE9PSIsInZhbHVlIjoiZmwyN1hyVHd4b2JiaWNHL2Q2L1ZwU3AwSEErTlQ0T0F1bmJHZVVFMlBzNUowaHR0R0Y2dWxyOHNIajVxK3lRMjJHSVFlYmJVd3dpbUZLNDZSeXA1bTBuc2FjWmk0TFUzRVBpYWRaOGFZQUVYSzdMVm9QeUltR1RVYVJneGlOT29ZWjJSTjNNQU1VMHptL0VwbWZRRndPUXJUYTU4dDRJa0k3dlpabEF2Nnh0cWVOZ1pYY2pFMFZzb0U3NmViZit4WVhvWjFJeEFseEtjOGpPb2RveE1QeEo1dEdZK2lEbG5iQ2tISGVlbzI0dzB1Mzh3cnhIZzNJL2dSaTQ0TnV4a1JIUmVSeUJDSnczM0FRaWpML09uaFBtUHFNck1NeXh2eWpTQXJmbStjYUV2dStQU0lTRXg4VVJybHltYm5KbXg0Z2NHSEpZQW9jbEYvZGZubHhSa3N4S2tyM2tUNlEvTkZNMkhPckt0ZUhCSkx6dm5JejZDYldRT2NBM2RxTHBRR1RLb1FTVjFUWXJRVUlhdU1yWTBpZm9NOGNwY0Rrd1lnK2RRMDNvY2FwOCtBNStJZlA0WVhIZWl0MGxWdll4YmF5OEhFc3VoL3lBTVcyeFdUb2M5Z080RXdteFliaVpDTkIzL3hiUUxMd1N5RElkYWlpdFNUSHJ4YStsTUhBUCsiLCJtYWMiOiJjMTQzMDllYzMxOWFhYTczZTQ5ZGE3NTczNGU2MzE3YzcxOWQ1Yjc1M2MxMmE4NWJlZjllNDgwZmU2NDg0ODlhIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Im1wZDJ0K0lzM3NqbURUbHJ0MElTQnc9PSIsInZhbHVlIjoiSDhNZ2JKR29ZWXJDaTVONVBjRGQrWXIzRG5PcThKUEJxb0VHY3ZLZDlpMUswcDVOb2kwYXgwRUxBTVhGMHBGVjJPMHJTc0VvVXptTlRJdFRiNmdMNThDU0hPcm1TTmU0SXlhKzdranQ0UkxRSXcrRStLUENRV3g0ZWo5NVQrS0h6aG93N3RoNWUxVUdpZGVVQm5HMlpIL0ExRUhaOHZlNk5EQmhPblpQMndyc1VIb09PNzhUajVRbU1ONGZZOXRScE1HMHdvNEFTRlJVNXloREh4cXRCNnREOUtDOGhSTzRWR04rVk9kUGpjWnhjUStjbmUwQ0thY3FXak52YnBNWFVmWXRTaUJxZlRJN3NGNko3TXNHQzc2cWl3KzJoWmM0YW9xdXBrSk9mclA2OXo4TWkwLzRYTHN6ODgrRzU4SnpzdFZiY0l0YmlIbzdZdWNxY3IyN2Z5Wm1Uakk0UWQ1eHo3UU9YUE5BeFRWVjRlQXpCeXNNZHFJWkpxMS8wdlFqR3FmaWE4bWhFdHNqNHpaYzdMRm9VejEvOFJ3WHZ2TGVTTjc2WHFxNjU4ajlKMGZMVEZCa0Q0SVIvN21QTmFVTmNXN2dMVU4yNTdpNG1UbnZzWk1FNmRqWUJWY3hRNDJQRW9DcHA4S1k5Y04yRjlac2h4aHR4ZTJJSDlaazloQ2IiLCJtYWMiOiIzZjkwMDZmMmNmOTc5MjdlODliZjNiYjVkNTg2YmRkYTIyNzM3MmNlZmM3ZmM5ZmM4MTY0MDNhZmJkOWIxYWJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1817809157\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-579559105 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NnOpBkKlzDCYP0gC0gA26hvqmORlKbmYh1U3VF2B</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWSxIFjNu1s5OpxXmNdhn5W8ul114PqK4OYo41zx</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0aKMqKkrxmyGXbPQ7Du4TVovUvHLOwr5Gqmkro7o</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-579559105\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1540881330 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 01:13:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1540881330\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-253938819 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWSxIFjNu1s5OpxXmNdhn5W8ul114PqK4OYo41zx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/calendar/all</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-253938819\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/public-bookings", "action_name": "public-bookings.store", "controller_action": "App\\Http\\Controllers\\BookingController@publicStore"}, "badge": null}}