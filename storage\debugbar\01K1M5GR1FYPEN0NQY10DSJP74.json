{"__meta": {"id": "01K1M5GR1FYPEN0NQY10DSJP74", "datetime": "2025-08-02 01:14:39", "utime": **********.026867, "method": "GET", "uri": "/cookie-consent?cookie%5B%5D=necessary", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": 1754097277.854684, "end": **********.026921, "duration": 1.1722369194030762, "duration_str": "1.17s", "measures": [{"label": "Booting", "start": 1754097277.854684, "relative_start": 0, "end": **********.423129, "relative_end": **********.423129, "duration": 0.****************, "duration_str": "568ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.423139, "relative_start": 0.****************, "end": **********.026927, "relative_end": 5.9604644775390625e-06, "duration": 0.***************, "duration_str": "604ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.433855, "relative_start": 0.****************, "end": **********.450607, "relative_end": **********.450607, "duration": 0.016752004623413086, "duration_str": "16.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.016679, "relative_start": 1.****************, "end": **********.018085, "relative_end": **********.018085, "duration": 0.001405954360961914, "duration_str": "1.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET cookie-consent", "middleware": "web", "controller": "App\\Http\\Controllers\\SystemController@CookieConsent<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=2385\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "cookie-consent", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=2385\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/SystemController.php:2385-2437</a>"}, "queries": {"count": 1, "nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01918, "accumulated_duration_str": "19.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\SystemController.php", "line": 2388}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.4757829, "duration": 0.01918, "duration_str": "19.18ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": {"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "new_omx_saas", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pU567Aq2SVwJ2XM2LnzZXFEdygvVxNd7VEsQmTe7", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/calendar/all\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/cookie-consent?cookie%5B0%5D=necessary", "action_name": "cookie-consent", "controller_action": "App\\Http\\Controllers\\SystemController@CookieConsent", "uri": "GET cookie-consent", "controller": "App\\Http\\Controllers\\SystemController@CookieConsent<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=2385\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=2385\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/SystemController.php:2385-2437</a>", "middleware": "web", "duration": "1.18s", "peak_memory": "56MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-942602928 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">necessary</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-942602928\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1865160907 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1865160907\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-446236683 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">XSRF-TOKEN=eyJpdiI6IlA3ZnQ1ZTdGeWxoZVVqRS9xQ3JabXc9PSIsInZhbHVlIjoiMmJ2ZHVkYUtXb0owc2VacHd2VTVEVGt3U1hvSTNWV0p4Ukp4MlIyaEJBQlRUZmxxcmRKdEt2a3RzVXJZVzdPYkZ2ZEZURUtYaDVGWi9XWXkyMzJJWjA2TTh2c1B2Rk9sUVh5MXhkSGlmQndtOGtQS0U1cXJJYWgyMVhQVitYcUZyeWNFcVpScVo4VjdIRWc1V2xCdjBONjc3SzA3cnpTL05LSXpiT255TjFpcXBIZmFiOFNiMkc0N3FZYmpFZW1oTkVUR2FNV1hUVGVCcndrYjRlKzBITlNvUzNQV3ljMXFvMXo3SFJjUU16bUJFZ05jQzMwa0lic1N2QTFBRmR4akFYWEJvY2FNdkxBbG83dTJBZ25WWHk2R3A2dVZLY2dDdjJneEd1Z3pCT3JnMnA1OXh5czhYckRBbmg1VEFZSk5PVXB4TVY3OUJaRTJSZ3dOb25aUWpKNFJwTlBUM0hUOUZhSTJsejFkcWgyUVVEbVE0Nk9nK3VrSjlrN01haGVyZDVBMjVJWTFnQnVXdXdjb3RWZUFoZmdSK253K2RjMktBRXVoYUtGV0tCR1VUdGlIc0U3UTNWb1VMZ3o5YmdmbUNCbE9VWFVKVFZBY1o5cVBDWWw0TWJoZkVOemc1cXBBL3hlNk9lRE9za0JMTXJYMWZ4Qlk3ckJSUHc2Z05EdCsiLCJtYWMiOiJhNTdhMzJiMjAxNmMxYTdiMGY3YTlmZDgyYTQ4MzcyYjE4YWJkYmU3OTAyMmIwYTgwNWNiOWY5MmY0OGY2MDkyIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjRuc3FCRFVHQVVyZEg3N0ZkZ1FJNkE9PSIsInZhbHVlIjoicEJrak04TmZSdFd2VExuRWJNczFxd3VqTGxReFBvS3U4RmJreEJSVHZxbUVvbXhUdHlvSWgzeVQrSlcrL25Bc3FlWTNqWDlFUGlwMmFnQkZFS2QvMjVqUkhtU2x3c21MQURialN0QkJoWm9LZkE0TXVscVlabUg4ZWFqV1BQVi9NMDRrd2hHSGd5RU1zc294SnVJL1c3V1pkV0dud2tlU2hsa3NEbFJUNnpjMUNMMHdyakZZSWVHUmljc25uVGJBNWRIUTMvK0NXY1pXM0ZTaEd4dmFJQXRIc2FOWi9nZ0pwUFAzV3d3elFvbUJaRlk1WE1mSGFHcDNXSmQ5S1d6azFZTjRUbXNKRW55K2pFYk5QZk41TENRdnNaVDRqdk8rOEt3NllUbmRDYzJGNlFUZGlHUlRTK2NNemlXK3NOUXNDNk9rVmhScnhlb1JyQkllV2U5Ly9XYnR6cWtzcCsrM3gvS1VETTJJd2xhTGRyRGl0OFAwTlo0Mk9meVdSVUVsMjVKdTlxTGgvbXFHSmpldWVFZjVkeVF3QW5OM3JvNHZuTFdjSm1xM2ZtTzlaYmRHSGtSV2NBRUFPUllocUJrdlJrTU1XV2dqcjV1a3J6M1pVdUp3b080OXZFVW5aYWdETXY5VWRSaHU2L1JDWjhseXFEbDdNdmVWZHFSZjloMWEiLCJtYWMiOiI3ZDNiNzE2NjM1NGI4NzJlNzRlZWFiOTIxN2Q1MGU0YzVjOTk1OTU1OTM3ZjdjMjY2NDZlYjk1ZWE3NjhiZGI2IiwidGFnIjoiIn0%3D; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-446236683\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1931912922 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pU567Aq2SVwJ2XM2LnzZXFEdygvVxNd7VEsQmTe7</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EUHcfWULB3vVCc41aWPaYoFNlTgsLiUR14En3RAA</span>\"\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1931912922\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-507554654 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 01:14:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-507554654\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-106150757 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pU567Aq2SVwJ2XM2LnzZXFEdygvVxNd7VEsQmTe7</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/calendar/all</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-106150757\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/cookie-consent?cookie%5B0%5D=necessary", "action_name": "cookie-consent", "controller_action": "App\\Http\\Controllers\\SystemController@CookieConsent"}, "badge": null}}