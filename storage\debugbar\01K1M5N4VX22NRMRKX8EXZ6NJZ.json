{"__meta": {"id": "01K1M5N4VX22NRMRKX8EXZ6NJZ", "datetime": "2025-08-02 01:17:03", "utime": **********.229862, "method": "GET", "uri": "/calendar-events/2", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[01:17:00] LOG.error: Undefined array key \"id\" {\n    \"view\": {\n        \"view\": \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new_laravel_project\\\\omx-new-saas\\\\resources\\\\views\\\\tasks\\\\copy_event.blade.php\",\n        \"data\": {\n            \"errors\": \"<pre class=sf-dump id=sf-dump-85885579 data-indent-pad=\\\"  \\\"><span class=sf-dump-note>Illuminate\\\\Support\\\\ViewErrorBag<\\/span> {<a class=sf-dump-ref>#4678<\\/a><samp data-depth=1 class=sf-dump-expanded>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">bags<\\/span>: []\\n<\\/samp>}\\n<\\/pre><script>Sfdump(\\\"sf-dump-85885579\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"event\": \"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\\\"  \\\"><span class=sf-dump-note>App\\\\Models\\\\CalendarEvent<\\/span> {<a class=sf-dump-ref>#4699<\\/a><samp data-depth=1 class=sf-dump-expanded>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">connection<\\/span>: \\\"<span class=sf-dump-str title=\\\"5 characters\\\">mysql<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">table<\\/span>: \\\"<span class=sf-dump-str title=\\\"15 characters\\\">calendar_events<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">primaryKey<\\/span>: \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">keyType<\\/span>: \\\"<span class=sf-dump-str title=\\\"3 characters\\\">int<\\/span>\\\"\\n  +<span class=sf-dump-public title=\\\"Public property\\\">incrementing<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">with<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">withCount<\\/span>: []\\n  +<span class=sf-dump-public title=\\\"Public property\\\">preventsLazyLoading<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">perPage<\\/span>: <span class=sf-dump-num>15<\\/span>\\n  +<span class=sf-dump-public title=\\\"Public property\\\">exists<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  +<span class=sf-dump-public title=\\\"Public property\\\">wasRecentlyCreated<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">attributes<\\/span>: <span class=sf-dump-note>array:35<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>id<\\/span>\\\" => <span class=sf-dump-num>2<\\/span>\\n    \\\"<span class=sf-dump-key>title<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"10 characters\\\">deep event<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>start_date<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-07-10 08:44:00<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>end_date<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-07-17 08:44:00<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>duration<\\/span>\\\" => <span class=sf-dump-num>60<\\/span>\\n    \\\"<span class=sf-dump-key>booking_per_slot<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>minimum_notice<\\/span>\\\" => <span class=sf-dump-num>0<\\/span>\\n    \\\"<span class=sf-dump-key>minimum_notice_value<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>minimum_notice_unit<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">minutes<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>description<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"26 characters\\\">this is the booking system<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>payment_amount<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>payment_required<\\/span>\\\" => <span class=sf-dump-num>0<\\/span>\\n    \\\"<span class=sf-dump-key>payment_currency<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"3 characters\\\">USD<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>location<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"9 characters\\\">in_person<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>locations_data<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>custom_redirect_url<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>meet_link<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>physical_address<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"11 characters\\\">sifuadsfioa<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>require_name<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>require_email<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>require_phone<\\/span>\\\" => <span class=sf-dump-num>0<\\/span>\\n    \\\"<span class=sf-dump-key>custom_field<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>custom_field_value<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>weekly_availability<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>created_by<\\/span>\\\" => <span class=sf-dump-num>74<\\/span>\\n    \\\"<span class=sf-dump-key>assigned_staff_id<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>status<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"6 characters\\\">active<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>date_range_type<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>date_range_days<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>date_range_start<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>date_range_end<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>created_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-07-14 05:15:12<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>updated_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-07-17 13:20:32<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>date_override<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>custom_fields<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"42 characters\\\">[{&quot;type&quot;:&quot;full_name&quot;,&quot;label&quot;:&quot;Full Name&quot;}]<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">original<\\/span>: <span class=sf-dump-note>array:35<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>id<\\/span>\\\" => <span class=sf-dump-num>2<\\/span>\\n    \\\"<span class=sf-dump-key>title<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"10 characters\\\">deep event<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>start_date<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-07-10 08:44:00<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>end_date<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-07-17 08:44:00<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>duration<\\/span>\\\" => <span class=sf-dump-num>60<\\/span>\\n    \\\"<span class=sf-dump-key>booking_per_slot<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>minimum_notice<\\/span>\\\" => <span class=sf-dump-num>0<\\/span>\\n    \\\"<span class=sf-dump-key>minimum_notice_value<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>minimum_notice_unit<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">minutes<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>description<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"26 characters\\\">this is the booking system<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>payment_amount<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>payment_required<\\/span>\\\" => <span class=sf-dump-num>0<\\/span>\\n    \\\"<span class=sf-dump-key>payment_currency<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"3 characters\\\">USD<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>location<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"9 characters\\\">in_person<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>locations_data<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>custom_redirect_url<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>meet_link<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>physical_address<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"11 characters\\\">sifuadsfioa<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>require_name<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>require_email<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>require_phone<\\/span>\\\" => <span class=sf-dump-num>0<\\/span>\\n    \\\"<span class=sf-dump-key>custom_field<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>custom_field_value<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>weekly_availability<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>created_by<\\/span>\\\" => <span class=sf-dump-num>74<\\/span>\\n    \\\"<span class=sf-dump-key>assigned_staff_id<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>status<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"6 characters\\\">active<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>date_range_type<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>date_range_days<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>date_range_start<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>date_range_end<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>created_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-07-14 05:15:12<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>updated_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-07-17 13:20:32<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>date_override<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>custom_fields<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"42 characters\\\">[{&quot;type&quot;:&quot;full_name&quot;,&quot;label&quot;:&quot;Full Name&quot;}]<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">changes<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">casts<\\/span>: <span class=sf-dump-note>array:18<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>start_date<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"8 characters\\\">datetime<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>end_date<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"8 characters\\\">datetime<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>duration<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">integer<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>booking_per_slot<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">integer<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>minimum_notice<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">integer<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>require_name<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">boolean<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>require_email<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">boolean<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>require_phone<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">boolean<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>payment_required<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">boolean<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>payment_amount<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"9 characters\\\">decimal:2<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>custom_fields<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"5 characters\\\">array<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>custom_field_value<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"5 characters\\\">array<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>date_override<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"5 characters\\\">array<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>weekly_availability<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"5 characters\\\">array<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>locations_data<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"5 characters\\\">array<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>date_range_days<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">integer<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>date_range_start<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"4 characters\\\">date<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>date_range_end<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"4 characters\\\">date<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">classCastCache<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">attributeCastCache<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">dateFormat<\\/span>: <span class=sf-dump-const>null<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">appends<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">dispatchesEvents<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">observables<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">relations<\\/span>: <span class=sf-dump-note>array:2<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>weeklyAvailability<\\/span>\\\" => <span class=\\\"sf-dump-note sf-dump-ellipsization\\\" title=\\\"Illuminate\\\\Database\\\\Eloquent\\\\Collection\\n\\\"><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">Illuminate\\\\Database\\\\Eloquent<\\/span><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">\\\\<\\/span><span class=\\\"sf-dump-ellipsis-tail\\\">Collection<\\/span><\\/span> {<a class=sf-dump-ref>#4723<\\/a><samp data-depth=3 class=sf-dump-compact>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">items<\\/span>: <span class=sf-dump-note>array:7<\\/span> [ &#8230;7]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n    <\\/samp>}\\n    \\\"<span class=sf-dump-key>bookings<\\/span>\\\" => <span class=\\\"sf-dump-note sf-dump-ellipsization\\\" title=\\\"Illuminate\\\\Database\\\\Eloquent\\\\Collection\\n\\\"><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">Illuminate\\\\Database\\\\Eloquent<\\/span><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">\\\\<\\/span><span class=\\\"sf-dump-ellipsis-tail\\\">Collection<\\/span><\\/span> {<a class=sf-dump-ref>#456<\\/a><samp data-depth=3 class=sf-dump-compact>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">items<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n    <\\/samp>}\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">touches<\\/span>: []\\n  +<span class=sf-dump-public title=\\\"Public property\\\">timestamps<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  +<span class=sf-dump-public title=\\\"Public property\\\">usesUniqueIds<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">hidden<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">visible<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">fillable<\\/span>: <span class=sf-dump-note>array:32<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    <span class=sf-dump-index>0<\\/span> => \\\"<span class=sf-dump-str title=\\\"5 characters\\\">title<\\/span>\\\"\\n    <span class=sf-dump-index>1<\\/span> => \\\"<span class=sf-dump-str title=\\\"10 characters\\\">start_date<\\/span>\\\"\\n    <span class=sf-dump-index>2<\\/span> => \\\"<span class=sf-dump-str title=\\\"8 characters\\\">end_date<\\/span>\\\"\\n    <span class=sf-dump-index>3<\\/span> => \\\"<span class=sf-dump-str title=\\\"8 characters\\\">duration<\\/span>\\\"\\n    <span class=sf-dump-index>4<\\/span> => \\\"<span class=sf-dump-str title=\\\"16 characters\\\">booking_per_slot<\\/span>\\\"\\n    <span class=sf-dump-index>5<\\/span> => \\\"<span class=sf-dump-str title=\\\"14 characters\\\">minimum_notice<\\/span>\\\"\\n    <span class=sf-dump-index>6<\\/span> => \\\"<span class=sf-dump-str title=\\\"20 characters\\\">minimum_notice_value<\\/span>\\\"\\n    <span class=sf-dump-index>7<\\/span> => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">minimum_notice_unit<\\/span>\\\"\\n    <span class=sf-dump-index>8<\\/span> => \\\"<span class=sf-dump-str title=\\\"11 characters\\\">description<\\/span>\\\"\\n    <span class=sf-dump-index>9<\\/span> => \\\"<span class=sf-dump-str title=\\\"14 characters\\\">payment_amount<\\/span>\\\"\\n    <span class=sf-dump-index>10<\\/span> => \\\"<span class=sf-dump-str title=\\\"16 characters\\\">payment_required<\\/span>\\\"\\n    <span class=sf-dump-index>11<\\/span> => \\\"<span class=sf-dump-str title=\\\"16 characters\\\">payment_currency<\\/span>\\\"\\n    <span class=sf-dump-index>12<\\/span> => \\\"<span class=sf-dump-str title=\\\"8 characters\\\">location<\\/span>\\\"\\n    <span class=sf-dump-index>13<\\/span> => \\\"<span class=sf-dump-str title=\\\"14 characters\\\">locations_data<\\/span>\\\"\\n    <span class=sf-dump-index>14<\\/span> => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">custom_redirect_url<\\/span>\\\"\\n    <span class=sf-dump-index>15<\\/span> => \\\"<span class=sf-dump-str title=\\\"9 characters\\\">meet_link<\\/span>\\\"\\n    <span class=sf-dump-index>16<\\/span> => \\\"<span class=sf-dump-str title=\\\"16 characters\\\">physical_address<\\/span>\\\"\\n    <span class=sf-dump-index>17<\\/span> => \\\"<span class=sf-dump-str title=\\\"12 characters\\\">require_name<\\/span>\\\"\\n    <span class=sf-dump-index>18<\\/span> => \\\"<span class=sf-dump-str title=\\\"13 characters\\\">require_email<\\/span>\\\"\\n    <span class=sf-dump-index>19<\\/span> => \\\"<span class=sf-dump-str title=\\\"13 characters\\\">require_phone<\\/span>\\\"\\n    <span class=sf-dump-index>20<\\/span> => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">weekly_availability<\\/span>\\\"\\n    <span class=sf-dump-index>21<\\/span> => \\\"<span class=sf-dump-str title=\\\"12 characters\\\">custom_field<\\/span>\\\"\\n    <span class=sf-dump-index>22<\\/span> => \\\"<span class=sf-dump-str title=\\\"18 characters\\\">custom_field_value<\\/span>\\\"\\n    <span class=sf-dump-index>23<\\/span> => \\\"<span class=sf-dump-str title=\\\"13 characters\\\">custom_fields<\\/span>\\\"\\n    <span class=sf-dump-index>24<\\/span> => \\\"<span class=sf-dump-str title=\\\"10 characters\\\">created_by<\\/span>\\\"\\n    <span class=sf-dump-index>25<\\/span> => \\\"<span class=sf-dump-str title=\\\"17 characters\\\">assigned_staff_id<\\/span>\\\"\\n    <span class=sf-dump-index>26<\\/span> => \\\"<span class=sf-dump-str title=\\\"13 characters\\\">date_override<\\/span>\\\"\\n    <span class=sf-dump-index>27<\\/span> => \\\"<span class=sf-dump-str title=\\\"6 characters\\\">status<\\/span>\\\"\\n    <span class=sf-dump-index>28<\\/span> => \\\"<span class=sf-dump-str title=\\\"15 characters\\\">date_range_type<\\/span>\\\"\\n    <span class=sf-dump-index>29<\\/span> => \\\"<span class=sf-dump-str title=\\\"15 characters\\\">date_range_days<\\/span>\\\"\\n    <span class=sf-dump-index>30<\\/span> => \\\"<span class=sf-dump-str title=\\\"16 characters\\\">date_range_start<\\/span>\\\"\\n    <span class=sf-dump-index>31<\\/span> => \\\"<span class=sf-dump-str title=\\\"14 characters\\\">date_range_end<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">guarded<\\/span>: <span class=sf-dump-note>array:1<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    <span class=sf-dump-index>0<\\/span> => \\\"<span class=sf-dump-str>*<\\/span>\\\"\\n  <\\/samp>]\\n<\\/samp>}\\n<\\/pre><script>Sfdump(\\\"sf-dump-*********\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"company_payment_setting\": \"<pre class=sf-dump id=sf-dump-1556705890 data-indent-pad=\\\"  \\\"><span class=sf-dump-note>array:14<\\/span> [<samp data-depth=1 class=sf-dump-expanded>\\n  \\\"<span class=sf-dump-key>payment_currency<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"3 characters\\\">INR<\\/span>\\\"\\n  \\\"<span class=sf-dump-key>payment_currency_symbol<\\/span>\\\" => \\\"<span class=sf-dump-str>&#8377;<\\/span>\\\"\\n  \\\"<span class=sf-dump-key>is_manual_enabled<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"3 characters\\\">off<\\/span>\\\"\\n  \\\"<span class=sf-dump-key>is_bank_transfer_enabled<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"3 characters\\\">off<\\/span>\\\"\\n  \\\"<span class=sf-dump-key>is_stripe_enabled<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"3 characters\\\">off<\\/span>\\\"\\n  \\\"<span class=sf-dump-key>is_paypal_enabled<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"3 characters\\\">off<\\/span>\\\"\\n  \\\"<span class=sf-dump-key>is_razorpay_enabled<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"2 characters\\\">on<\\/span>\\\"\\n  \\\"<span class=sf-dump-key>razorpay_public_key<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"23 characters\\\">rzp_test_2SLQhcDaBpxSl0<\\/span>\\\"\\n  \\\"<span class=sf-dump-key>razorpay_secret_key<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"24 characters\\\">Eov8FEWkB3Tty2h1c2W8syw1<\\/span>\\\"\\n  \\\"<span class=sf-dump-key>is_paystack_enabled<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"3 characters\\\">off<\\/span>\\\"\\n  \\\"<span class=sf-dump-key>is_flutterwave_enabled<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"3 characters\\\">off<\\/span>\\\"\\n  \\\"<span class=sf-dump-key>paypal_mode<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"4 characters\\\">live<\\/span>\\\"\\n  \\\"<span class=sf-dump-key>paypal_client_id<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">2561526<\\/span>\\\"\\n  \\\"<span class=sf-dump-key>paypal_secret_key<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"4 characters\\\">mhgg<\\/span>\\\"\\n<\\/samp>]\\n<\\/pre><script>Sfdump(\\\"sf-dump-1556705890\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\"\n        }\n    },\n    \"userId\": 74,\n    \"exception\": {}\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.188756, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 4, "start": **********.189588, "end": **********.229892, "duration": 4.040303945541382, "duration_str": "4.04s", "measures": [{"label": "Booting", "start": **********.189588, "relative_start": 0, "end": **********.718957, "relative_end": **********.718957, "duration": 0.****************, "duration_str": "529ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.719004, "relative_start": 0.****************, "end": **********.229895, "relative_end": 3.0994415283203125e-06, "duration": 3.****************, "duration_str": "3.51s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.728334, "relative_start": 0.****************, "end": **********.744983, "relative_end": **********.744983, "duration": 0.*****************, "duration_str": "16.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.843675, "relative_start": 0.****************, "end": **********.229904, "relative_end": 1.1920928955078125e-05, "duration": 3.****************, "duration_str": "3.39s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "56MB"}, "exceptions": {"count": 2, "exceptions": [{"type": "Illuminate\\View\\ViewException", "message": "Undefined array key \"id\" (View: C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views\\tasks\\copy_event.blade.php)", "code": 0, "file": "storage/framework/views/f11c52ab0907a675e93c64d07a80ca9f.php", "line": 615, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:69</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>60</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">handleViewException</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">[object ErrorException]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>1</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\View\\Engines\\PhpEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"116 characters\">C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\storage\\framework\\views/f11c52ab0907a675e93c64d07a80ca9f.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref2330 title=\"2 occurrences\">#330</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref27 title=\"2 occurrences\">#7</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24678 title=\"2 occurrences\">#4678</a><samp data-depth=5 id=sf-dump-*********-ref24678 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []\n        </samp>}\n        \"<span class=sf-dump-key>event</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\CalendarEvent\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CalendarEvent</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24699 title=\"2 occurrences\">#4699</a><samp data-depth=5 id=sf-dump-*********-ref24699 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"15 characters\">calendar_events</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:35</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"10 characters\">deep event</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 08:44:00</span>\"\n            \"<span class=sf-dump-key>end_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 08:44:00</span>\"\n            \"<span class=sf-dump-key>duration</span>\" => <span class=sf-dump-num>60</span>\n            \"<span class=sf-dump-key>booking_per_slot</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>minimum_notice</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>minimum_notice_value</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>minimum_notice_unit</span>\" => \"<span class=sf-dump-str title=\"7 characters\">minutes</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"26 characters\">this is the booking system</span>\"\n            \"<span class=sf-dump-key>payment_amount</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>payment_required</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>payment_currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n            \"<span class=sf-dump-key>location</span>\" => \"<span class=sf-dump-str title=\"9 characters\">in_person</span>\"\n            \"<span class=sf-dump-key>locations_data</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>custom_redirect_url</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>meet_link</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>physical_address</span>\" => \"<span class=sf-dump-str title=\"11 characters\">sifuadsfioa</span>\"\n            \"<span class=sf-dump-key>require_name</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>require_email</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>require_phone</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>custom_field</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>custom_field_value</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>weekly_availability</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n            \"<span class=sf-dump-key>assigned_staff_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n            \"<span class=sf-dump-key>date_range_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>date_range_days</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>date_range_start</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>date_range_end</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-14 05:15:12</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n            \"<span class=sf-dump-key>date_override</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>custom_fields</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[{&quot;type&quot;:&quot;full_name&quot;,&quot;label&quot;:&quot;Full Name&quot;}]</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:35</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"10 characters\">deep event</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 08:44:00</span>\"\n            \"<span class=sf-dump-key>end_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 08:44:00</span>\"\n            \"<span class=sf-dump-key>duration</span>\" => <span class=sf-dump-num>60</span>\n            \"<span class=sf-dump-key>booking_per_slot</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>minimum_notice</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>minimum_notice_value</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>minimum_notice_unit</span>\" => \"<span class=sf-dump-str title=\"7 characters\">minutes</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"26 characters\">this is the booking system</span>\"\n            \"<span class=sf-dump-key>payment_amount</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>payment_required</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>payment_currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n            \"<span class=sf-dump-key>location</span>\" => \"<span class=sf-dump-str title=\"9 characters\">in_person</span>\"\n            \"<span class=sf-dump-key>locations_data</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>custom_redirect_url</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>meet_link</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>physical_address</span>\" => \"<span class=sf-dump-str title=\"11 characters\">sifuadsfioa</span>\"\n            \"<span class=sf-dump-key>require_name</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>require_email</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>require_phone</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>custom_field</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>custom_field_value</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>weekly_availability</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n            \"<span class=sf-dump-key>assigned_staff_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n            \"<span class=sf-dump-key>date_range_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>date_range_days</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>date_range_start</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>date_range_end</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-14 05:15:12</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n            \"<span class=sf-dump-key>date_override</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>custom_fields</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[{&quot;type&quot;:&quot;full_name&quot;,&quot;label&quot;:&quot;Full Name&quot;}]</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n            \"<span class=sf-dump-key>end_date</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n            \"<span class=sf-dump-key>duration</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>booking_per_slot</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>minimum_notice</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>require_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>require_email</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>require_phone</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>payment_required</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>payment_amount</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:2</span>\"\n            \"<span class=sf-dump-key>custom_fields</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>custom_field_value</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>date_override</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>weekly_availability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>locations_data</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>date_range_days</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>date_range_start</span>\" => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n            \"<span class=sf-dump-key>date_range_end</span>\" => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>weeklyAvailability</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#4723</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\EventWeeklyAvailability\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">EventWeeklyAvailability</span></span> {<a class=sf-dump-ref>#4719</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"25 characters\">event_weekly_availability</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>36</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"6 characters\">monday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>36</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"6 characters\">monday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">calendar_event_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">day_of_week</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">start_time</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">end_time</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n                <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\EventWeeklyAvailability\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">EventWeeklyAvailability</span></span> {<a class=sf-dump-ref>#4721</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"25 characters\">event_weekly_availability</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>37</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"7 characters\">tuesday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>37</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"7 characters\">tuesday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">calendar_event_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">day_of_week</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">start_time</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">end_time</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n                <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\EventWeeklyAvailability\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">EventWeeklyAvailability</span></span> {<a class=sf-dump-ref>#4747</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"25 characters\">event_weekly_availability</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>38</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"9 characters\">wednesday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>38</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"9 characters\">wednesday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">calendar_event_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">day_of_week</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">start_time</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">end_time</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n                <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\EventWeeklyAvailability\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">EventWeeklyAvailability</span></span> {<a class=sf-dump-ref>#4746</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"25 characters\">event_weekly_availability</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>39</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"8 characters\">thursday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>39</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"8 characters\">thursday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">calendar_event_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">day_of_week</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">start_time</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">end_time</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n                <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\EventWeeklyAvailability\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">EventWeeklyAvailability</span></span> {<a class=sf-dump-ref>#4745</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"25 characters\">event_weekly_availability</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>40</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"6 characters\">friday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>40</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"6 characters\">friday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">calendar_event_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">day_of_week</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">start_time</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">end_time</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n                <span class=sf-dump-index>5</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\EventWeeklyAvailability\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">EventWeeklyAvailability</span></span> {<a class=sf-dump-ref>#4744</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"25 characters\">event_weekly_availability</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>41</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"8 characters\">saturday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>41</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"8 characters\">saturday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">calendar_event_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">day_of_week</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">start_time</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">end_time</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n                <span class=sf-dump-index>6</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\EventWeeklyAvailability\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">EventWeeklyAvailability</span></span> {<a class=sf-dump-ref>#4743</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"25 characters\">event_weekly_availability</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>42</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"6 characters\">sunday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>42</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"6 characters\">sunday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">calendar_event_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">day_of_week</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">start_time</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">end_time</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            \"<span class=sf-dump-key>bookings</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#456</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Booking\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Booking</span></span> {<a class=sf-dump-ref>#4732</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">bookings</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:19</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n                    \"<span class=sf-dump-key>event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Deep Sarkar</span>\"\n                    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"15 characters\"><EMAIL></span>\"\n                    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">9547990733</span>\"\n                    \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-15</span>\"\n                    \"<span class=sf-dump-key>time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">11:30:00</span>\"\n                    \"<span class=sf-dump-key>selected_location</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">scheduled</span>\"\n                    \"<span class=sf-dump-key>payment_amount</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">pending</span>\"\n                    \"<span class=sf-dump-key>payment_method</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>payment_transaction_id</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>payment_date</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>payment_details</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>custom_fields</span>\" => \"<span class=sf-dump-str title=\"13 characters\">[&quot;full_name&quot;]</span>\"\n                    \"<span class=sf-dump-key>custom_fields_value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">[&quot;Deep Sarkar&quot;]</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-14 05:15:49</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-14 05:15:49</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:19</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n                    \"<span class=sf-dump-key>event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Deep Sarkar</span>\"\n                    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"15 characters\"><EMAIL></span>\"\n                    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">9547990733</span>\"\n                    \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-15</span>\"\n                    \"<span class=sf-dump-key>time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">11:30:00</span>\"\n                    \"<span class=sf-dump-key>selected_location</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">scheduled</span>\"\n                    \"<span class=sf-dump-key>payment_amount</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">pending</span>\"\n                    \"<span class=sf-dump-key>payment_method</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>payment_transaction_id</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>payment_date</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>payment_details</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>custom_fields</span>\" => \"<span class=sf-dump-str title=\"13 characters\">[&quot;full_name&quot;]</span>\"\n                    \"<span class=sf-dump-key>custom_fields_value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">[&quot;Deep Sarkar&quot;]</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-14 05:15:49</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-14 05:15:49</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>custom_fields</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                    \"<span class=sf-dump-key>custom_fields_value</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                    \"<span class=sf-dump-key>selected_location</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                    \"<span class=sf-dump-key>payment_amount</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:2</span>\"\n                    \"<span class=sf-dump-key>payment_date</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                    \"<span class=sf-dump-key>payment_details</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:16</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">event_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n                    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"4 characters\">time</span>\"\n                    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"17 characters\">selected_location</span>\"\n                    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"13 characters\">custom_fields</span>\"\n                    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"19 characters\">custom_fields_value</span>\"\n                    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">payment_amount</span>\"\n                    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"14 characters\">payment_status</span>\"\n                    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"14 characters\">payment_method</span>\"\n                    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"22 characters\">payment_transaction_id</span>\"\n                    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"12 characters\">payment_date</span>\"\n                    <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"15 characters\">payment_details</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">start_date</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">end_date</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">duration</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"16 characters\">booking_per_slot</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"14 characters\">minimum_notice</span>\"\n            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"20 characters\">minimum_notice_value</span>\"\n            <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"19 characters\">minimum_notice_unit</span>\"\n            <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n            <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"14 characters\">payment_amount</span>\"\n            <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"16 characters\">payment_required</span>\"\n            <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"16 characters\">payment_currency</span>\"\n            <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"8 characters\">location</span>\"\n            <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"14 characters\">locations_data</span>\"\n            <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"19 characters\">custom_redirect_url</span>\"\n            <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"9 characters\">meet_link</span>\"\n            <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"16 characters\">physical_address</span>\"\n            <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"12 characters\">require_name</span>\"\n            <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"13 characters\">require_email</span>\"\n            <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"13 characters\">require_phone</span>\"\n            <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"19 characters\">weekly_availability</span>\"\n            <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"12 characters\">custom_field</span>\"\n            <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"18 characters\">custom_field_value</span>\"\n            <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"13 characters\">custom_fields</span>\"\n            <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n            <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"17 characters\">assigned_staff_id</span>\"\n            <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"13 characters\">date_override</span>\"\n            <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n            <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"15 characters\">date_range_type</span>\"\n            <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"15 characters\">date_range_days</span>\"\n            <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"16 characters\">date_range_start</span>\"\n            <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">date_range_end</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n        </samp>}\n        \"<span class=sf-dump-key>company_payment_setting</span>\" => <span class=sf-dump-note>array:14</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>payment_currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">INR</span>\"\n          \"<span class=sf-dump-key>payment_currency_symbol</span>\" => \"<span class=sf-dump-str>&#8377;</span>\"\n          \"<span class=sf-dump-key>is_manual_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>is_bank_transfer_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>is_stripe_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>is_paypal_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>is_razorpay_enabled</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n          \"<span class=sf-dump-key>razorpay_public_key</span>\" => \"<span class=sf-dump-str title=\"23 characters\">rzp_test_2SLQhcDaBpxSl0</span>\"\n          \"<span class=sf-dump-key>razorpay_secret_key</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Eov8FEWkB3Tty2h1c2W8syw1</span>\"\n          \"<span class=sf-dump-key>is_paystack_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>is_flutterwave_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>paypal_mode</span>\" => \"<span class=sf-dump-str title=\"4 characters\">live</span>\"\n          \"<span class=sf-dump-key>paypal_client_id</span>\" => \"<span class=sf-dump-str title=\"7 characters\">2561526</span>\"\n          \"<span class=sf-dump-key>paypal_secret_key</span>\" => \"<span class=sf-dump-str title=\"4 characters\">mhgg</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"98 characters\">C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/tasks/copy_event.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref2330 title=\"2 occurrences\">#330</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref27 title=\"2 occurrences\">#7</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24678 title=\"2 occurrences\">#4678</a>}\n        \"<span class=sf-dump-key>event</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\CalendarEvent\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CalendarEvent</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24699 title=\"2 occurrences\">#4699</a>}\n        \"<span class=sf-dump-key>company_payment_setting</span>\" => <span class=sf-dump-note>array:14</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>payment_currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">INR</span>\"\n          \"<span class=sf-dump-key>payment_currency_symbol</span>\" => \"<span class=sf-dump-str>&#8377;</span>\"\n          \"<span class=sf-dump-key>is_manual_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>is_bank_transfer_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>is_stripe_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>is_paypal_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>is_razorpay_enabled</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n          \"<span class=sf-dump-key>razorpay_public_key</span>\" => \"<span class=sf-dump-str title=\"23 characters\">rzp_test_2SLQhcDaBpxSl0</span>\"\n          \"<span class=sf-dump-key>razorpay_secret_key</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Eov8FEWkB3Tty2h1c2W8syw1</span>\"\n          \"<span class=sf-dump-key>is_paystack_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>is_flutterwave_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>paypal_mode</span>\" => \"<span class=sf-dump-str title=\"4 characters\">live</span>\"\n          \"<span class=sf-dump-key>paypal_client_id</span>\" => \"<span class=sf-dump-str title=\"7 characters\">2561526</span>\"\n          \"<span class=sf-dump-key>paypal_secret_key</span>\" => \"<span class=sf-dump-str title=\"4 characters\">mhgg</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>192</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">getContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>161</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">renderContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>79</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">render</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>35</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">setContent</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>920</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">__construct</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>200</span>\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>Content-Type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">text/html</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>887</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">toResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">prepareResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"50 characters\">app/Http/Middleware/EnsurePermissionsAfterPost.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"46 characters\">App\\Http\\Middleware\\EnsurePermissionsAfterPost</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"37 characters\">app/Http/Middleware/FilterRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>26</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Http\\Middleware\\FilterRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>88</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>121</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>201</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>110</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>110</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>63</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>64</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>65</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>176</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>66</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>145</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>67</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>68</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"72 characters\">C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["                <?php elseif($field['type'] === 'email'): ?>\r\n", "                  <input type=\"email\" class=\"form-control\" id=\"custom_field_<?php echo e($index); ?>\" name=\"custom_fields[<?php echo e($field['id']); ?>]\" placeholder=\"Enter <?php echo e(strtolower($field['name'] ?? 'value')); ?>\">\r\n", "                <?php else: ?>\r\n", "                  <input type=\"text\" class=\"form-control\" id=\"custom_field_<?php echo e($index); ?>\" name=\"custom_fields[<?php echo e($field['id']); ?>]\" placeholder=\"Enter <?php echo e(strtolower($field['name'] ?? 'value')); ?>\">\r\n", "                <?php endif; ?>\r\n", "              </div>\r\n", "            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>\r\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fstorage%2Fframework%2Fviews%2Ff11c52ab0907a675e93c64d07a80ca9f.php&line=615", "ajax": false, "filename": "f11c52ab0907a675e93c64d07a80ca9f.php", "line": "615"}}, {"type": "ErrorException", "message": "Undefined array key \"id\"", "code": 0, "file": "storage/framework/views/f11c52ab0907a675e93c64d07a80ca9f.php", "line": 615, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:73</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/HandleExceptions.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>256</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">handleError</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Foundation\\Bootstrap\\HandleExceptions</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-num>2</span>\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"24 characters\">Undefined array key &quot;id&quot;</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"116 characters\">C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\storage\\framework\\views\\f11c52ab0907a675e93c64d07a80ca9f.php</span>\"\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-num>615</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"60 characters\">storage/framework/views/f11c52ab0907a675e93c64d07a80ca9f.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>615</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Illuminate\\Foundation\\Bootstrap\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Foundation\\Bootstrap\\HandleExceptions</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-num>2</span>\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"24 characters\">Undefined array key &quot;id&quot;</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"116 characters\">C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\storage\\framework\\views\\f11c52ab0907a675e93c64d07a80ca9f.php</span>\"\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-num>615</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>123</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"116 characters\">C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\storage\\framework\\views\\f11c52ab0907a675e93c64d07a80ca9f.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">require</span>\"\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>124</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Filesystem\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Filesystem\\Filesystem</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">getRequire</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Filesystem\\Filesystem</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"116 characters\">C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\storage\\framework\\views/f11c52ab0907a675e93c64d07a80ca9f.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref2330 title=\"3 occurrences\">#330</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref27 title=\"3 occurrences\">#7</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24678 title=\"3 occurrences\">#4678</a><samp data-depth=5 id=sf-dump-*********-ref24678 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []\n        </samp>}\n        \"<span class=sf-dump-key>event</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\CalendarEvent\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CalendarEvent</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24699 title=\"3 occurrences\">#4699</a><samp data-depth=5 id=sf-dump-*********-ref24699 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"15 characters\">calendar_events</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:35</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"10 characters\">deep event</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 08:44:00</span>\"\n            \"<span class=sf-dump-key>end_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 08:44:00</span>\"\n            \"<span class=sf-dump-key>duration</span>\" => <span class=sf-dump-num>60</span>\n            \"<span class=sf-dump-key>booking_per_slot</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>minimum_notice</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>minimum_notice_value</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>minimum_notice_unit</span>\" => \"<span class=sf-dump-str title=\"7 characters\">minutes</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"26 characters\">this is the booking system</span>\"\n            \"<span class=sf-dump-key>payment_amount</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>payment_required</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>payment_currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n            \"<span class=sf-dump-key>location</span>\" => \"<span class=sf-dump-str title=\"9 characters\">in_person</span>\"\n            \"<span class=sf-dump-key>locations_data</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>custom_redirect_url</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>meet_link</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>physical_address</span>\" => \"<span class=sf-dump-str title=\"11 characters\">sifuadsfioa</span>\"\n            \"<span class=sf-dump-key>require_name</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>require_email</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>require_phone</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>custom_field</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>custom_field_value</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>weekly_availability</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n            \"<span class=sf-dump-key>assigned_staff_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n            \"<span class=sf-dump-key>date_range_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>date_range_days</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>date_range_start</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>date_range_end</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-14 05:15:12</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n            \"<span class=sf-dump-key>date_override</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>custom_fields</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[{&quot;type&quot;:&quot;full_name&quot;,&quot;label&quot;:&quot;Full Name&quot;}]</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:35</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"10 characters\">deep event</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 08:44:00</span>\"\n            \"<span class=sf-dump-key>end_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 08:44:00</span>\"\n            \"<span class=sf-dump-key>duration</span>\" => <span class=sf-dump-num>60</span>\n            \"<span class=sf-dump-key>booking_per_slot</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>minimum_notice</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>minimum_notice_value</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>minimum_notice_unit</span>\" => \"<span class=sf-dump-str title=\"7 characters\">minutes</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"26 characters\">this is the booking system</span>\"\n            \"<span class=sf-dump-key>payment_amount</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>payment_required</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>payment_currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n            \"<span class=sf-dump-key>location</span>\" => \"<span class=sf-dump-str title=\"9 characters\">in_person</span>\"\n            \"<span class=sf-dump-key>locations_data</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>custom_redirect_url</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>meet_link</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>physical_address</span>\" => \"<span class=sf-dump-str title=\"11 characters\">sifuadsfioa</span>\"\n            \"<span class=sf-dump-key>require_name</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>require_email</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>require_phone</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>custom_field</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>custom_field_value</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>weekly_availability</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n            \"<span class=sf-dump-key>assigned_staff_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n            \"<span class=sf-dump-key>date_range_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>date_range_days</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>date_range_start</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>date_range_end</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-14 05:15:12</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n            \"<span class=sf-dump-key>date_override</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>custom_fields</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[{&quot;type&quot;:&quot;full_name&quot;,&quot;label&quot;:&quot;Full Name&quot;}]</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n            \"<span class=sf-dump-key>end_date</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n            \"<span class=sf-dump-key>duration</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>booking_per_slot</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>minimum_notice</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>require_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>require_email</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>require_phone</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>payment_required</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>payment_amount</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:2</span>\"\n            \"<span class=sf-dump-key>custom_fields</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>custom_field_value</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>date_override</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>weekly_availability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>locations_data</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>date_range_days</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>date_range_start</span>\" => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n            \"<span class=sf-dump-key>date_range_end</span>\" => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>weeklyAvailability</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#4723</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\EventWeeklyAvailability\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">EventWeeklyAvailability</span></span> {<a class=sf-dump-ref>#4719</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"25 characters\">event_weekly_availability</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>36</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"6 characters\">monday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>36</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"6 characters\">monday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">calendar_event_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">day_of_week</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">start_time</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">end_time</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n                <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\EventWeeklyAvailability\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">EventWeeklyAvailability</span></span> {<a class=sf-dump-ref>#4721</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"25 characters\">event_weekly_availability</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>37</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"7 characters\">tuesday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>37</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"7 characters\">tuesday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">calendar_event_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">day_of_week</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">start_time</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">end_time</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n                <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\EventWeeklyAvailability\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">EventWeeklyAvailability</span></span> {<a class=sf-dump-ref>#4747</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"25 characters\">event_weekly_availability</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>38</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"9 characters\">wednesday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>38</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"9 characters\">wednesday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">calendar_event_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">day_of_week</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">start_time</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">end_time</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n                <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\EventWeeklyAvailability\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">EventWeeklyAvailability</span></span> {<a class=sf-dump-ref>#4746</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"25 characters\">event_weekly_availability</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>39</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"8 characters\">thursday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>39</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"8 characters\">thursday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">calendar_event_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">day_of_week</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">start_time</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">end_time</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n                <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\EventWeeklyAvailability\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">EventWeeklyAvailability</span></span> {<a class=sf-dump-ref>#4745</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"25 characters\">event_weekly_availability</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>40</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"6 characters\">friday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>40</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"6 characters\">friday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">calendar_event_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">day_of_week</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">start_time</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">end_time</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n                <span class=sf-dump-index>5</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\EventWeeklyAvailability\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">EventWeeklyAvailability</span></span> {<a class=sf-dump-ref>#4744</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"25 characters\">event_weekly_availability</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>41</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"8 characters\">saturday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>41</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"8 characters\">saturday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">calendar_event_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">day_of_week</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">start_time</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">end_time</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n                <span class=sf-dump-index>6</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\EventWeeklyAvailability\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">EventWeeklyAvailability</span></span> {<a class=sf-dump-ref>#4743</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"25 characters\">event_weekly_availability</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>42</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"6 characters\">sunday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>42</span>\n                    \"<span class=sf-dump-key>calendar_event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>day_of_week</span>\" => \"<span class=sf-dump-str title=\"6 characters\">sunday</span>\"\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">09:00:00</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">17:00:00</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 13:20:32</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>start_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                    \"<span class=sf-dump-key>end_time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">calendar_event_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">day_of_week</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">start_time</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">end_time</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            \"<span class=sf-dump-key>bookings</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#456</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Booking\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Booking</span></span> {<a class=sf-dump-ref>#4732</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">bookings</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:19</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n                    \"<span class=sf-dump-key>event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Deep Sarkar</span>\"\n                    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"15 characters\"><EMAIL></span>\"\n                    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">9547990733</span>\"\n                    \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-15</span>\"\n                    \"<span class=sf-dump-key>time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">11:30:00</span>\"\n                    \"<span class=sf-dump-key>selected_location</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">scheduled</span>\"\n                    \"<span class=sf-dump-key>payment_amount</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">pending</span>\"\n                    \"<span class=sf-dump-key>payment_method</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>payment_transaction_id</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>payment_date</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>payment_details</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>custom_fields</span>\" => \"<span class=sf-dump-str title=\"13 characters\">[&quot;full_name&quot;]</span>\"\n                    \"<span class=sf-dump-key>custom_fields_value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">[&quot;Deep Sarkar&quot;]</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-14 05:15:49</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-14 05:15:49</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:19</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n                    \"<span class=sf-dump-key>event_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Deep Sarkar</span>\"\n                    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"15 characters\"><EMAIL></span>\"\n                    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">9547990733</span>\"\n                    \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-15</span>\"\n                    \"<span class=sf-dump-key>time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">11:30:00</span>\"\n                    \"<span class=sf-dump-key>selected_location</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">scheduled</span>\"\n                    \"<span class=sf-dump-key>payment_amount</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">pending</span>\"\n                    \"<span class=sf-dump-key>payment_method</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>payment_transaction_id</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>payment_date</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>payment_details</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>custom_fields</span>\" => \"<span class=sf-dump-str title=\"13 characters\">[&quot;full_name&quot;]</span>\"\n                    \"<span class=sf-dump-key>custom_fields_value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">[&quot;Deep Sarkar&quot;]</span>\"\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-14 05:15:49</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-14 05:15:49</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>custom_fields</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                    \"<span class=sf-dump-key>custom_fields_value</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                    \"<span class=sf-dump-key>selected_location</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                    \"<span class=sf-dump-key>payment_amount</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:2</span>\"\n                    \"<span class=sf-dump-key>payment_date</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                    \"<span class=sf-dump-key>payment_details</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:16</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">event_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n                    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"4 characters\">time</span>\"\n                    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"17 characters\">selected_location</span>\"\n                    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"13 characters\">custom_fields</span>\"\n                    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"19 characters\">custom_fields_value</span>\"\n                    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">payment_amount</span>\"\n                    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"14 characters\">payment_status</span>\"\n                    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"14 characters\">payment_method</span>\"\n                    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"22 characters\">payment_transaction_id</span>\"\n                    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"12 characters\">payment_date</span>\"\n                    <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"15 characters\">payment_details</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">start_date</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">end_date</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">duration</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"16 characters\">booking_per_slot</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"14 characters\">minimum_notice</span>\"\n            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"20 characters\">minimum_notice_value</span>\"\n            <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"19 characters\">minimum_notice_unit</span>\"\n            <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n            <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"14 characters\">payment_amount</span>\"\n            <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"16 characters\">payment_required</span>\"\n            <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"16 characters\">payment_currency</span>\"\n            <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"8 characters\">location</span>\"\n            <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"14 characters\">locations_data</span>\"\n            <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"19 characters\">custom_redirect_url</span>\"\n            <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"9 characters\">meet_link</span>\"\n            <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"16 characters\">physical_address</span>\"\n            <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"12 characters\">require_name</span>\"\n            <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"13 characters\">require_email</span>\"\n            <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"13 characters\">require_phone</span>\"\n            <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"19 characters\">weekly_availability</span>\"\n            <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"12 characters\">custom_field</span>\"\n            <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"18 characters\">custom_field_value</span>\"\n            <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"13 characters\">custom_fields</span>\"\n            <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n            <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"17 characters\">assigned_staff_id</span>\"\n            <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"13 characters\">date_override</span>\"\n            <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n            <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"15 characters\">date_range_type</span>\"\n            <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"15 characters\">date_range_days</span>\"\n            <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"16 characters\">date_range_start</span>\"\n            <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">date_range_end</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n        </samp>}\n        \"<span class=sf-dump-key>company_payment_setting</span>\" => <span class=sf-dump-note>array:14</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>payment_currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">INR</span>\"\n          \"<span class=sf-dump-key>payment_currency_symbol</span>\" => \"<span class=sf-dump-str>&#8377;</span>\"\n          \"<span class=sf-dump-key>is_manual_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>is_bank_transfer_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>is_stripe_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>is_paypal_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>is_razorpay_enabled</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n          \"<span class=sf-dump-key>razorpay_public_key</span>\" => \"<span class=sf-dump-str title=\"23 characters\">rzp_test_2SLQhcDaBpxSl0</span>\"\n          \"<span class=sf-dump-key>razorpay_secret_key</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Eov8FEWkB3Tty2h1c2W8syw1</span>\"\n          \"<span class=sf-dump-key>is_paystack_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>is_flutterwave_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>paypal_mode</span>\" => \"<span class=sf-dump-str title=\"4 characters\">live</span>\"\n          \"<span class=sf-dump-key>paypal_client_id</span>\" => \"<span class=sf-dump-str title=\"7 characters\">2561526</span>\"\n          \"<span class=sf-dump-key>paypal_secret_key</span>\" => \"<span class=sf-dump-str title=\"4 characters\">mhgg</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\View\\Engines\\PhpEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"116 characters\">C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\storage\\framework\\views/f11c52ab0907a675e93c64d07a80ca9f.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref2330 title=\"3 occurrences\">#330</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref27 title=\"3 occurrences\">#7</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24678 title=\"3 occurrences\">#4678</a>}\n        \"<span class=sf-dump-key>event</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\CalendarEvent\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CalendarEvent</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24699 title=\"3 occurrences\">#4699</a>}\n        \"<span class=sf-dump-key>company_payment_setting</span>\" => <span class=sf-dump-note>array:14</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>payment_currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">INR</span>\"\n          \"<span class=sf-dump-key>payment_currency_symbol</span>\" => \"<span class=sf-dump-str>&#8377;</span>\"\n          \"<span class=sf-dump-key>is_manual_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>is_bank_transfer_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>is_stripe_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>is_paypal_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>is_razorpay_enabled</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n          \"<span class=sf-dump-key>razorpay_public_key</span>\" => \"<span class=sf-dump-str title=\"23 characters\">rzp_test_2SLQhcDaBpxSl0</span>\"\n          \"<span class=sf-dump-key>razorpay_secret_key</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Eov8FEWkB3Tty2h1c2W8syw1</span>\"\n          \"<span class=sf-dump-key>is_paystack_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>is_flutterwave_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>paypal_mode</span>\" => \"<span class=sf-dump-str title=\"4 characters\">live</span>\"\n          \"<span class=sf-dump-key>paypal_client_id</span>\" => \"<span class=sf-dump-str title=\"7 characters\">2561526</span>\"\n          \"<span class=sf-dump-key>paypal_secret_key</span>\" => \"<span class=sf-dump-str title=\"4 characters\">mhgg</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"98 characters\">C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/tasks/copy_event.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref2330 title=\"3 occurrences\">#330</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref27 title=\"3 occurrences\">#7</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24678 title=\"3 occurrences\">#4678</a>}\n        \"<span class=sf-dump-key>event</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\CalendarEvent\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CalendarEvent</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24699 title=\"3 occurrences\">#4699</a>}\n        \"<span class=sf-dump-key>company_payment_setting</span>\" => <span class=sf-dump-note>array:14</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>payment_currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">INR</span>\"\n          \"<span class=sf-dump-key>payment_currency_symbol</span>\" => \"<span class=sf-dump-str>&#8377;</span>\"\n          \"<span class=sf-dump-key>is_manual_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>is_bank_transfer_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>is_stripe_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>is_paypal_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>is_razorpay_enabled</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n          \"<span class=sf-dump-key>razorpay_public_key</span>\" => \"<span class=sf-dump-str title=\"23 characters\">rzp_test_2SLQhcDaBpxSl0</span>\"\n          \"<span class=sf-dump-key>razorpay_secret_key</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Eov8FEWkB3Tty2h1c2W8syw1</span>\"\n          \"<span class=sf-dump-key>is_paystack_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>is_flutterwave_enabled</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n          \"<span class=sf-dump-key>paypal_mode</span>\" => \"<span class=sf-dump-str title=\"4 characters\">live</span>\"\n          \"<span class=sf-dump-key>paypal_client_id</span>\" => \"<span class=sf-dump-str title=\"7 characters\">2561526</span>\"\n          \"<span class=sf-dump-key>paypal_secret_key</span>\" => \"<span class=sf-dump-str title=\"4 characters\">mhgg</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>192</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">getContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>161</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">renderContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>79</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">render</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>35</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">setContent</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>920</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">__construct</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>200</span>\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>Content-Type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">text/html</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>887</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">toResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">prepareResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"50 characters\">app/Http/Middleware/EnsurePermissionsAfterPost.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"46 characters\">App\\Http\\Middleware\\EnsurePermissionsAfterPost</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"37 characters\">app/Http/Middleware/FilterRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>26</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Http\\Middleware\\FilterRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>88</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>121</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>201</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>110</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>110</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>63</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>64</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>65</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>66</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>67</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>68</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>69</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>176</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>70</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>145</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>71</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>72</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"72 characters\">C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["                <?php elseif($field['type'] === 'email'): ?>\r\n", "                  <input type=\"email\" class=\"form-control\" id=\"custom_field_<?php echo e($index); ?>\" name=\"custom_fields[<?php echo e($field['id']); ?>]\" placeholder=\"Enter <?php echo e(strtolower($field['name'] ?? 'value')); ?>\">\r\n", "                <?php else: ?>\r\n", "                  <input type=\"text\" class=\"form-control\" id=\"custom_field_<?php echo e($index); ?>\" name=\"custom_fields[<?php echo e($field['id']); ?>]\" placeholder=\"Enter <?php echo e(strtolower($field['name'] ?? 'value')); ?>\">\r\n", "                <?php endif; ?>\r\n", "              </div>\r\n", "            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>\r\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fstorage%2Fframework%2Fviews%2Ff11c52ab0907a675e93c64d07a80ca9f.php&line=615", "ajax": false, "filename": "f11c52ab0907a675e93c64d07a80ca9f.php", "line": "615"}}]}, "views": {"count": 9, "nb_templates": 9, "templates": [{"name": "9x tasks.copy_event", "param_count": null, "params": [], "start": **********.846821, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/tasks/copy_event.blade.phptasks.copy_event", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Ftasks%2Fcopy_event.blade.php&line=1", "ajax": false, "filename": "copy_event.blade.php", "line": "?"}, "render_count": 9, "name_original": "tasks.copy_event"}]}, "route": {"uri": "GET calendar-events/{id}", "middleware": "web", "controller": "App\\Http\\Controllers\\CalendarEventController@publicView<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FCalendarEventController.php&line=462\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "calendar-events.show", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FCalendarEventController.php&line=462\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/CalendarEventController.php:462-479</a>"}, "queries": {"count": 5, "nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.05566, "accumulated_duration_str": "55.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `calendar_events` where `calendar_events`.`id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/CalendarEventController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\CalendarEventController.php", "line": 465}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.758035, "duration": 0.012960000000000001, "duration_str": "12.96ms", "memory": 0, "memory_str": null, "filename": "CalendarEventController.php:465", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/CalendarEventController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\CalendarEventController.php", "line": 465}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FCalendarEventController.php&line=465", "ajax": false, "filename": "CalendarEventController.php", "line": "465"}, "connection": "new_omx_saas", "explain": null, "start_percent": 0, "width_percent": 23.284}, {"sql": "select * from `event_weekly_availability` where `event_weekly_availability`.`calendar_event_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/CalendarEventController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\CalendarEventController.php", "line": 465}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.780499, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "CalendarEventController.php:465", "source": {"index": 23, "namespace": null, "name": "app/Http/Controllers/CalendarEventController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\CalendarEventController.php", "line": 465}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FCalendarEventController.php&line=465", "ajax": false, "filename": "CalendarEventController.php", "line": "465"}, "connection": "new_omx_saas", "explain": null, "start_percent": 23.284, "width_percent": 1.276}, {"sql": "select * from `bookings` where `bookings`.`event_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/CalendarEventController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\CalendarEventController.php", "line": 465}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.784715, "duration": 0.04007, "duration_str": "40.07ms", "memory": 0, "memory_str": null, "filename": "CalendarEventController.php:465", "source": {"index": 23, "namespace": null, "name": "app/Http/Controllers/CalendarEventController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\CalendarEventController.php", "line": 465}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FCalendarEventController.php&line=465", "ajax": false, "filename": "CalendarEventController.php", "line": "465"}, "connection": "new_omx_saas", "explain": null, "start_percent": 24.56, "width_percent": 71.991}, {"sql": "select * from `company_payment_settings` where `created_by` = 74", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 3539}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/CalendarEventController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\CalendarEventController.php", "line": 468}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.833497, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Utility.php:3539", "source": {"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 3539}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=3539", "ajax": false, "filename": "Utility.php", "line": "3539"}, "connection": "new_omx_saas", "explain": null, "start_percent": 96.55, "width_percent": 1.617}, {"sql": "select * from `users` where `id` = 74 limit 1", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 251}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php", "line": 539}], "start": **********.183482, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "new_omx_saas", "explain": null, "start_percent": 98.167, "width_percent": 1.833}]}, "models": {"data": {"App\\Models\\EventWeeklyAvailability": {"retrieved": 7, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FEventWeeklyAvailability.php&line=1", "ajax": false, "filename": "EventWeeklyAvailability.php", "line": "?"}}, "App\\Models\\CalendarEvent": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FCalendarEvent.php&line=1", "ajax": false, "filename": "CalendarEvent.php", "line": "?"}}, "App\\Models\\Booking": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FBooking.php&line=1", "ajax": false, "filename": "Booking.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 10, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 10}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWSxIFjNu1s5OpxXmNdhn5W8ul114PqK4OYo41zx", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/booking-confirmation/6?success=1\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "74", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "500 Internal Server Error", "full_url": "http://127.0.0.1:8000/calendar-events/2", "action_name": "calendar-events.show", "controller_action": "App\\Http\\Controllers\\CalendarEventController@publicView", "uri": "GET calendar-events/{id}", "controller": "App\\Http\\Controllers\\CalendarEventController@publicView<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FCalendarEventController.php&line=462\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FCalendarEventController.php&line=462\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/CalendarEventController.php:462-479</a>", "middleware": "web", "duration": "4.08s", "peak_memory": "62MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1193931415 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1193931415\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1671624315 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1671624315\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1568943590 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjZVREVBbC9XSnIwcnNtSE5nWGdkbWc9PSIsInZhbHVlIjoiSUZqRHlsTHl1NWFFNVYzbTdnRFhIdW5DUjY0RVFjV01mOXFNSnU5aFpBcnpsV3BWT21OZUwyM0pVMWxNT2VYZUlEa1A2WHU4cDYveHF6NVF3N1ZnV3lNY2pLM1BLUkJOMlZJZXJ0SUxnTjJJSC9BYmNKbHp1V1FlWGZNL3IweVM4Mi9jcUw4U3ZBYnZoY2JaRTkzbGo2L2pSUlVxMGk3b0wxUWZ2d2JkSUlwTDZnbUFsc3BRSGh2ZVRnZFBiV1o5aTlZcXlncElWVDA2VXdhei8wVEE2bXQzWkpUbSthTDI4eDNOaGtja0ZMVT0iLCJtYWMiOiJmYzk1YjdhMmFhYmZjYTM1ZjAxZWM4NTA5NWEyNWRmYjE1OWUxYjlkMTQ1NzUzMTY5NDM2YTk0Y2EyMDc5ZmI3IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6Im9La0FzZlE2YmtRMDlMT1VjMVRIcEE9PSIsInZhbHVlIjoiMUkvTE9PZ1hSUTUxUHBXcWtzZXlpWHQyenJmNEE4bkg1cDZMenFzbmZQUGkrbnNZWGRib2JpS2RZZVNGbWJRaXk1aFBUcTZ0bXlLMHkvWFQxL1lJK0RER2RjU0QrRFM5VE9uc211SlNnM1JKNllwTm9Nazhqbkk2MllKbzB1WkhROUQvYUhGS2tjbGhmb2ptdElSL2xhZlU2V0lNOGswVGJ6ZGhNM2JGbXNEWUVQUWZNYzdkcGd6bFdyL2ViU0JBcDJRb0R0NGRHeWtrUVEvV2N1NDZCZHBSdFJZSkR4Mmw4M01WVVQ4V0Y2UmZ2WGVTeHlhNWw3b3ZNam1XV3lEQU84Um0wQlZUQkhnbWlycVJnZVV4UXM5alVzbXpDV2NhcER6MjVRYXdRRmVlQlh1dncyaGV6NlZQZkJJSUhOVTdjbm9ZM2MyTm9PbFNVRmFLcVdXOGN6ZXBHeVppQ2JNZy9pKzhSVWkwRnRmZTk0dXFlckloaThzajk3b3B5bUEyT2tWUU1OR3NsK214czZCZkxTaWNsTWo0eTAzWXpFR0YyRE9aenFpbGtySFFyM1NzK2x0bDVvRGlmbnRObkxodlpPeDhsTVVWSFoyUG1MWEdmd08zMSsrbWZCUW01TjNFd0hBVk12b3hUMk5RdzNVSUN0V0pvQVdWTGtnaWRaZE0iLCJtYWMiOiJjOGNkMDY1OWZjMjQxNjU3MWQ4YmI1NDdjNGQzY2ZjMTE0Nzc2ZTg2NWYxNWYzNzc2YThmYjVmODg4YTg2ODg4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkZQbUw0VXhCTEV4cDFidjNBdkRhZ3c9PSIsInZhbHVlIjoiRVdkRTVyMkd6K2RHSDF5dmhjUVpZbjhwL1FiYU5hdnI3UkJkb1JkbHBYSlBDWk1jOXR0YktJU0ZOa0dTRHd6RDF6UC9DRnBjVG1IdGxOZ0hKdFBTTmRKS0xmSFdxRHR1L3l6Rkw5bmFEc2t2RWlkNk1mWC9FbUFwNU03L21LU200TG9uUXZmLzBsWDU3clY5Ly9CaDN3MTYxbFl5blhvUnArbC9LZ0RrcUZDVnhjTHVXMnlDQjA2NU9vdGNSZXIzQmtSNEVHNmFvT3U2RVdBRHVwbFlYRzZzait4MEJJSk5nYlhPK0dhNjBmZVF2cFRRQUVQRDVacHZwazB2Q0FES1dud29ySjJWN09PclFoTEV0N0FoNUZLaW1LRzE2UGdNSzBKRitna1h3K1hSWUlyOGRsczVYUEY5aSswYWl0cm50a1lNNUVTWjZHR0h5NXNnU3RkL2ptWlBVdVJYc3ZpcUg2UTgyMGQwS2VTUDU4Z29PQjVpUWdKZ3d3bHoxQW5UZWZxNkFUOHh4TGU4bi8yejR2VUgzSXBKRndYbGpQNVpQR0tEQy8reVoxRExsRlZUcmFXc3lHNWY5OThjN0xvWlBGa1NiTDRhQjVzOHFqNzNLUWxoaElhMzJiVGt0VlJHc0gxNTBjZFNmMTU2dGNVam4wSUdVYUVPcW5SQkZINTAiLCJtYWMiOiI2YzVlMDFhMjdmM2UyYzllNTA1ODVlYzAyYzQwZDhlOWNlNDUwYWQyYTdmODg4MGFlZjBlNjkwYzY3OWFjZmU2IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InlRZzdTWmxwTXVyZzdEQnBBekhyM1E9PSIsInZhbHVlIjoiMFhzL0FURURhdTRSeFhxVUVLOG80T3F0eksvTWVGdWV5cFlNcldnQzJlVUNITGI0ZXhvVXpHMVlnUk52NnE3V1JGKzE0eUVHTWoyRTY4S0RNbHZOSzFrQm9FWmpUWHRiRFMxTG9TTFljakdkM1JjbldSUTVhNlM1d0hiVmR4RTltQWZBUENQRWM3ZzhuaEFYTE9qR0tmV2w0ZkVmSmM4WG9Yd1JqSUs0TzRSOGNWam1tQ0U2UDVGeTI1Y2hFU1dSdHE4NjlNM2YwL0VkVE1VdW8rY29ySUpycXhLWnE3YlJoYklMK3RpL0ZpMmUxc3lwV0o1OE9WSVlOMFRqVXpzQlN0eitQdVBhT1NjaFhGUUgxd0hiRzJtNldSNlVlSG9MQ0l1WHQxWi80UGVYVDdZZk8rckd6ekRSdlRwK3dYelBrbmxxdEdrYk5uVGZHZnJjZVh3cDFydTRTS2c3THJHZGwxV05RS0tPYWlkZmJadVpGSzBaVFZXck10Nld6eEd1Z29pV1ErY1FiV2xlMENsZEdUeTB6MWdHTVBFTzh4K042Skh0VjQrQ1Z1SmxvSDlwajdaZHNvd0QydER4OURJakZnM0ZxTGZjZjljMGtHQ3pVb3B4UHRsczdrUGQ0d2psei8wNUlnTzUrMTdYSW05NDlWdXFjeWtKemk2UE9GTTUiLCJtYWMiOiI2NDczYjAzMzcwMGU3NGJhMTU3YTNjZTJjNWFmYWUxMWEyZTQ5ZWU3NjdlY2JjMzA1NDNiMzZlNjc2ODc5ZjlmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1568943590\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-613882033 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NnOpBkKlzDCYP0gC0gA26hvqmORlKbmYh1U3VF2B</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWSxIFjNu1s5OpxXmNdhn5W8ul114PqK4OYo41zx</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0aKMqKkrxmyGXbPQ7Du4TVovUvHLOwr5Gqmkro7o</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-613882033\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1586947800 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 01:17:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1586947800\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2049589261 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWSxIFjNu1s5OpxXmNdhn5W8ul114PqK4OYo41zx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"54 characters\">http://127.0.0.1:8000/booking-confirmation/6?success=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2049589261\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "500 Internal Server Error", "full_url": "http://127.0.0.1:8000/calendar-events/2", "action_name": "calendar-events.show", "controller_action": "App\\Http\\Controllers\\CalendarEventController@publicView"}, "badge": "500 Internal Server Error"}}