{"__meta": {"id": "01K1M5ZDGHQ7DPCFFMBQAXVW8Q", "datetime": "2025-08-02 01:22:39", "utime": **********.762232, "method": "POST", "uri": "/booking-payments/initialize", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 6, "messages": [{"message": "[01:22:39] LOG.info: Payment initialization request data: {\n    \"booking_id\": 8,\n    \"amount\": 500\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.679428, "xdebug_link": null, "collector": "log"}, {"message": "[01:22:39] LOG.info: Booking found - ID: 8, Event ID: 4, Event Creator: 74", "message_html": null, "is_string": false, "label": "info", "time": **********.699737, "xdebug_link": null, "collector": "log"}, {"message": "[01:22:39] LOG.info: Payment method: razorpay, Creator ID: 74", "message_html": null, "is_string": false, "label": "info", "time": **********.700007, "xdebug_link": null, "collector": "log"}, {"message": "[01:22:39] LOG.info: paymentConfig called with method: razorpay, creator_id: 74", "message_html": null, "is_string": false, "label": "info", "time": **********.700173, "xdebug_link": null, "collector": "log"}, {"message": "[01:22:39] LOG.info: Payment config - method: razorpay, enabled: on, public_key: rzp_test_2SLQhcDaBpxSl0", "message_html": null, "is_string": false, "label": "info", "time": **********.702933, "xdebug_link": null, "collector": "log"}, {"message": "[01:22:39] LOG.info: Payment initialization response: {\n    \"email\": \"<EMAIL>\",\n    \"total_price\": 500,\n    \"currency\": \"INR\",\n    \"booking_id\": 8,\n    \"payment_method\": \"razorpay\",\n    \"flag\": 1,\n    \"razorpay_key\": \"rzp_test_2SLQhcDaBpxSl0\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.754032, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 4, "start": **********.059492, "end": **********.762263, "duration": 0.7027709484100342, "duration_str": "703ms", "measures": [{"label": "Booting", "start": **********.059492, "relative_start": 0, "end": **********.608354, "relative_end": **********.608354, "duration": 0.****************, "duration_str": "549ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.608377, "relative_start": 0.****************, "end": **********.762265, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "154ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.617885, "relative_start": 0.****************, "end": **********.626797, "relative_end": **********.626797, "duration": 0.008911848068237305, "duration_str": "8.91ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.758617, "relative_start": 0.***************, "end": **********.759113, "relative_end": **********.759113, "duration": 0.0004961490631103516, "duration_str": "496μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST booking-payments/initialize", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\BookingPaymentController@initializePayment<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FBookingPaymentController.php&line=139\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "booking-payments.initialize", "prefix": "/booking-payments", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FBookingPaymentController.php&line=139\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/BookingPaymentController.php:139-226</a>"}, "queries": {"count": 7, "nb_statements": 7, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.062439999999999996, "accumulated_duration_str": "62.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 74 limit 1", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.651243, "duration": 0.015359999999999999, "duration_str": "15.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "new_omx_saas", "explain": null, "start_percent": 0, "width_percent": 24.6}, {"sql": "select * from `settings` where `created_by` = 74", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.676095, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": {"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "new_omx_saas", "explain": null, "start_percent": 24.6, "width_percent": 1.233}, {"sql": "select count(*) as aggregate from `bookings` where `id` = 8", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 984}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 955}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 686}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 481}], "start": **********.686981, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "new_omx_saas", "explain": null, "start_percent": 25.833, "width_percent": 1.137}, {"sql": "select * from `bookings` where `bookings`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/BookingPaymentController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\BookingPaymentController.php", "line": 151}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.6924741, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BookingPaymentController.php:151", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/BookingPaymentController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\BookingPaymentController.php", "line": 151}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FBookingPaymentController.php&line=151", "ajax": false, "filename": "BookingPaymentController.php", "line": "151"}, "connection": "new_omx_saas", "explain": null, "start_percent": 26.97, "width_percent": 1.025}, {"sql": "select * from `calendar_events` where `calendar_events`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/BookingPaymentController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\BookingPaymentController.php", "line": 151}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.697291, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "BookingPaymentController.php:151", "source": {"index": 23, "namespace": null, "name": "app/Http/Controllers/BookingPaymentController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\BookingPaymentController.php", "line": 151}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FBookingPaymentController.php&line=151", "ajax": false, "filename": "BookingPaymentController.php", "line": "151"}, "connection": "new_omx_saas", "explain": null, "start_percent": 27.995, "width_percent": 1.137}, {"sql": "select * from `company_payment_settings` where `created_by` = 74", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 3539}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/BookingPaymentController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\BookingPaymentController.php", "line": 36}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/BookingPaymentController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\BookingPaymentController.php", "line": 172}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.700341, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:3539", "source": {"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 3539}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=3539", "ajax": false, "filename": "Utility.php", "line": "3539"}, "connection": "new_omx_saas", "explain": null, "start_percent": 29.132, "width_percent": 0.961}, {"sql": "update `bookings` set `payment_method` = 'razorpay', `bookings`.`updated_at` = '2025-08-02 01:22:39' where `id` = 8", "type": "query", "params": [], "bindings": ["razorpay", "2025-08-02 01:22:39", 8], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BookingPaymentController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\BookingPaymentController.php", "line": 184}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.704579, "duration": 0.04365, "duration_str": "43.65ms", "memory": 0, "memory_str": null, "filename": "BookingPaymentController.php:184", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/BookingPaymentController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\BookingPaymentController.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FBookingPaymentController.php&line=184", "ajax": false, "filename": "BookingPaymentController.php", "line": "184"}, "connection": "new_omx_saas", "explain": null, "start_percent": 30.093, "width_percent": 69.907}]}, "models": {"data": {"App\\Models\\Booking": {"retrieved": 1, "updated": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FBooking.php&line=1", "ajax": false, "filename": "Booking.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\CalendarEvent": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FCalendarEvent.php&line=1", "ajax": false, "filename": "CalendarEvent.php", "line": "?"}}}, "count": 4, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 3, "updated": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWSxIFjNu1s5OpxXmNdhn5W8ul114PqK4OYo41zx", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/calendar-events/4\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "74", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/booking-payments/initialize", "action_name": "booking-payments.initialize", "controller_action": "App\\Http\\Controllers\\BookingPaymentController@initializePayment", "uri": "POST booking-payments/initialize", "controller": "App\\Http\\Controllers\\BookingPaymentController@initializePayment<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FBookingPaymentController.php&line=139\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/booking-payments", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FBookingPaymentController.php&line=139\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/BookingPaymentController.php:139-226</a>", "middleware": "web, verified, auth, XSS", "duration": "706ms", "peak_memory": "58MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1929041667 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1929041667\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1761371117 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>booking_id</span>\" => <span class=sf-dump-num>8</span>\n  \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>500</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1761371117\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1748709467 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">29</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWSxIFjNu1s5OpxXmNdhn5W8ul114PqK4OYo41zx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/calendar-events/4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjZVREVBbC9XSnIwcnNtSE5nWGdkbWc9PSIsInZhbHVlIjoiSUZqRHlsTHl1NWFFNVYzbTdnRFhIdW5DUjY0RVFjV01mOXFNSnU5aFpBcnpsV3BWT21OZUwyM0pVMWxNT2VYZUlEa1A2WHU4cDYveHF6NVF3N1ZnV3lNY2pLM1BLUkJOMlZJZXJ0SUxnTjJJSC9BYmNKbHp1V1FlWGZNL3IweVM4Mi9jcUw4U3ZBYnZoY2JaRTkzbGo2L2pSUlVxMGk3b0wxUWZ2d2JkSUlwTDZnbUFsc3BRSGh2ZVRnZFBiV1o5aTlZcXlncElWVDA2VXdhei8wVEE2bXQzWkpUbSthTDI4eDNOaGtja0ZMVT0iLCJtYWMiOiJmYzk1YjdhMmFhYmZjYTM1ZjAxZWM4NTA5NWEyNWRmYjE1OWUxYjlkMTQ1NzUzMTY5NDM2YTk0Y2EyMDc5ZmI3IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6Im9La0FzZlE2YmtRMDlMT1VjMVRIcEE9PSIsInZhbHVlIjoiMUkvTE9PZ1hSUTUxUHBXcWtzZXlpWHQyenJmNEE4bkg1cDZMenFzbmZQUGkrbnNZWGRib2JpS2RZZVNGbWJRaXk1aFBUcTZ0bXlLMHkvWFQxL1lJK0RER2RjU0QrRFM5VE9uc211SlNnM1JKNllwTm9Nazhqbkk2MllKbzB1WkhROUQvYUhGS2tjbGhmb2ptdElSL2xhZlU2V0lNOGswVGJ6ZGhNM2JGbXNEWUVQUWZNYzdkcGd6bFdyL2ViU0JBcDJRb0R0NGRHeWtrUVEvV2N1NDZCZHBSdFJZSkR4Mmw4M01WVVQ4V0Y2UmZ2WGVTeHlhNWw3b3ZNam1XV3lEQU84Um0wQlZUQkhnbWlycVJnZVV4UXM5alVzbXpDV2NhcER6MjVRYXdRRmVlQlh1dncyaGV6NlZQZkJJSUhOVTdjbm9ZM2MyTm9PbFNVRmFLcVdXOGN6ZXBHeVppQ2JNZy9pKzhSVWkwRnRmZTk0dXFlckloaThzajk3b3B5bUEyT2tWUU1OR3NsK214czZCZkxTaWNsTWo0eTAzWXpFR0YyRE9aenFpbGtySFFyM1NzK2x0bDVvRGlmbnRObkxodlpPeDhsTVVWSFoyUG1MWEdmd08zMSsrbWZCUW01TjNFd0hBVk12b3hUMk5RdzNVSUN0V0pvQVdWTGtnaWRaZE0iLCJtYWMiOiJjOGNkMDY1OWZjMjQxNjU3MWQ4YmI1NDdjNGQzY2ZjMTE0Nzc2ZTg2NWYxNWYzNzc2YThmYjVmODg4YTg2ODg4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im5XZXJWd0hyZEovWklXSW1vL2JBNXc9PSIsInZhbHVlIjoiOUdYM0dJNFRvcGhMeVFIcEFOOU1sd3FXSGxHWWc0azFqSzB5ejE5MW5oN2xJL2NPaXJlT3BreXVobCs3SVcwWDNxZkhURThVd0trNlJxZ2NVS09GN05qNmdMR0I5VERzeWtkTktMaVhKdDhTME9MVCtQcFBoR0dZY3RyMU1VdDBOWHF1bjNvKzU3OENsM1d6S3ZvUlpIV3pTQk1UVmFzRVcwNEN6TVhwWmMzc2FsNEsrQlBmWXZiQzVMdGdGc09hUW1FRURSZTBRZTUzSytQZ0N0dUdrSDRpUFliS1BPK09zLzZWT2hENHZEaGE2dnF2cFhkcEwxRHVmTXZTeTFOanVoV1A3VzlRcHpxck0rYmswVGw0MWxIMkQ5UTd5Q2RXUlZHbHZ4N2FrUUQ0Vm1yNU42dXRnYUJMVzB6Y1ozekNxREhXY0NMOGwxRDN0aGVlblRUdEhFemtTUjhYcERLSHBMZ3pmcDg3T28xNmYrWmlzN29FdHFhSW1tSDFBZjlXakRvYXl5a1ZlN2h3KzBFUDlLOE43bDVNMGRqcDRPYkczTC81RWQ3Nzd1djJ5QjEyTnlQbysyMDRIQWhTcm4yVjlKQWc0VnJ2RGdGd3g0cHVjc0tsQ3orSWlBekZIVXZIR25NaThnbEs0NUdnWDNVcjlaeUsvSEg2T3RJTzgxWG8iLCJtYWMiOiIzYjBkZDY0MzIyM2QyZGM4OTk5YzU2MzkxNmM4YmU0NWNjZmM4NjBiYWNiMTM5YzlhZGVmYzdlOTZjNzk1ZjJlIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InJveENkTmE3eEMyL1UrZHJ0aTF0NGc9PSIsInZhbHVlIjoiK2V0NWtvR0YvQkNWaGkyUGQ5TU5CMkdWRlJVRDdHYTJ1U2Z1enh0RGVSMlhRSkF5OUVPODdmcmsvRGdMWkdsR29ld0JEd0hvVklsOW01VkxiVEw2QmppN0UrUGhVaGc2TnlLSmtMZ2RSeENQTGhGcDdlaENEd3JBQXZFYnpjUENDN0xkZlh2Q25qK2ZDei96UUxRWnhjQjRDM04yZlMwMHpwckhPZS81MzZTekNZcklkMktyNW9FZ1FscGx6SXdiWmlramJFWkdWNmVBUVEyS2NxRzl1NTNNQWVkZTMveldDR0JKUHFtOStQVGpOU0I2OTEveE8rQlpmTVlSWDRha0lKNkZMUjZYU0x5N1F1SVNxeWJlTkxMNHJqZWZrVWRmZ2F0L0NsV2d3Ni9YYzRSU2tLZWVGWGkwS2JBR1RnWFEvRWwxSGVkdit4NUtyN0VkTk8ycktXeFZ1T3lwRXN1VllDOFNLTkdLZXowTnA0bUhHTFVMR3RPajcyUEEvb0RqVnQ5MlBPNTVERUh4K3M0UnJxTEFQbTBCVHo0emRJMTJNeGdjZExkN2VIdks2aDJiQTRWMWlzZWdVYThwME1UdnY1U3ZMeUZsYTlsTHYvQmx4cXo2WnZDMHFZV2ZUaWMycEJhbTZneXBmalRPcHAwZXFYVTgvajlBdFVwWUJIby8iLCJtYWMiOiJkN2RhZDQzNmI5YjQ2Yzg2NjdkYjMwYzI3NTk4Njc3MTA0ZWYzZDZkYTZiNTNjOWM4OGZmNzUxNWJlYTEyYTIwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1748709467\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1122407373 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NnOpBkKlzDCYP0gC0gA26hvqmORlKbmYh1U3VF2B</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWSxIFjNu1s5OpxXmNdhn5W8ul114PqK4OYo41zx</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0aKMqKkrxmyGXbPQ7Du4TVovUvHLOwr5Gqmkro7o</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1122407373\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1391670498 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 01:22:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1391670498\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-829632453 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWSxIFjNu1s5OpxXmNdhn5W8ul114PqK4OYo41zx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/calendar-events/4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-829632453\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/booking-payments/initialize", "action_name": "booking-payments.initialize", "controller_action": "App\\Http\\Controllers\\BookingPaymentController@initializePayment"}, "badge": null}}