{"__meta": {"id": "01K1M606CX2N2NJSHGRKTX6MER", "datetime": "2025-08-02 01:23:05", "utime": **********.247711, "method": "POST", "uri": "/public-bookings", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 3, "messages": [{"message": "[01:23:03] LOG.info: Starting webhook dispatch for action: booking.booking_form_submitted {\n    \"timestamp\": \"2025-08-02T01:23:03.134744Z\",\n    \"source\": \"crm_webhook_system\",\n    \"action\": \"booking.booking_form_submitted\",\n    \"user_id\": 74,\n    \"entity_type\": \"Booking\",\n    \"entity_id\": 9,\n    \"status\": \"dispatching\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.136101, "xdebug_link": null, "collector": "log"}, {"message": "[01:23:05] LOG.error: <PERSON><PERSON><PERSON> failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2045 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {\n    \"timestamp\": \"2025-08-02T01:23:05.212834Z\",\n    \"source\": \"crm_webhook_system\",\n    \"action\": \"booking.booking_form_submitted\",\n    \"module_name\": \"OMX FLOW\",\n    \"webhook_url\": \"http:\\/\\/127.0.0.1:2000\\/external-crm\\/webhook\",\n    \"status\": \"failed\",\n    \"status_code\": null,\n    \"response_time_ms\": 2072,\n    \"user_id\": 74,\n    \"entity_id\": 9,\n    \"entity_type\": null,\n    \"request_payload\": {\n        \"action\": \"booking.booking_form_submitted\",\n        \"timestamp\": \"2025-08-02T01:23:03.140821Z\",\n        \"data\": {\n            \"event_id\": 4,\n            \"name\": \"<PERSON><PERSON>man\",\n            \"email\": \"<EMAIL>\",\n            \"phone\": \"6295298485\",\n            \"date\": \"2025-08-02\",\n            \"time\": \"12:30\",\n            \"selected_location\": {\n                \"type\": \"in_person\",\n                \"value\": \"Siliguri\",\n                \"display\": \"In-person meeting\"\n            },\n            \"custom_fields\": [],\n            \"custom_fields_value\": [],\n            \"payment_amount\": \"500.00\",\n            \"payment_status\": \"pending\",\n            \"status\": \"scheduled\",\n            \"updated_at\": \"2025-08-02T01:23:03.000000Z\",\n            \"created_at\": \"2025-08-02T01:23:03.000000Z\",\n            \"id\": 9,\n            \"form_data\": {\n                \"name\": \"Mintu Barman\",\n                \"email\": \"<EMAIL>\",\n                \"phone\": \"6295298485\",\n                \"date\": \"2025-08-02\",\n                \"time\": \"12:30\",\n                \"selected_location\": {\n                    \"type\": \"in_person\",\n                    \"value\": \"Siliguri\",\n                    \"display\": \"In-person meeting\"\n                },\n                \"custom_fields\": [],\n                \"custom_fields_value\": [],\n                \"booking_type\": \"public\"\n            },\n            \"triggered_by\": {\n                \"user_id\": 74,\n                \"email\": \"<EMAIL>\",\n                \"name\": \"deep sha\",\n                \"type\": \"company\"\n            }\n        },\n        \"user_id\": 74,\n        \"source\": {\n            \"system\": \"krishna\",\n            \"version\": \"1.0\",\n            \"url\": \"http:\\/\\/localhost:8000\"\n        }\n    },\n    \"error_message\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2045 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/external-crm\\/webhook\",\n    \"response_body\": null\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.214024, "xdebug_link": null, "collector": "log"}, {"message": "[01:23:05] LOG.warning: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 1 {\n    \"timestamp\": \"2025-08-02T01:23:05.214593Z\",\n    \"source\": \"crm_webhook_system\",\n    \"action\": \"booking.booking_form_submitted\",\n    \"user_id\": 74,\n    \"status\": \"completed\",\n    \"total_modules\": 1,\n    \"successful_modules\": 0,\n    \"failed_modules\": 1,\n    \"modules\": [\n        \"OMX FLOW\"\n    ],\n    \"results\": {\n        \"OMX FLOW\": {\n            \"success\": false,\n            \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2045 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/external-crm\\/webhook\",\n            \"integration\": \"OMX FLOW\"\n        }\n    }\n}", "message_html": null, "is_string": false, "label": "warning", "time": **********.215284, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 4, "start": **********.378571, "end": **********.247777, "duration": 2.869205951690674, "duration_str": "2.87s", "measures": [{"label": "Booting", "start": **********.378571, "relative_start": 0, "end": **********.978306, "relative_end": **********.978306, "duration": 0.****************, "duration_str": "600ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.978316, "relative_start": 0.****************, "end": **********.247782, "relative_end": 5.0067901611328125e-06, "duration": 2.***************, "duration_str": "2.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.988414, "relative_start": 0.****************, "end": **********.999364, "relative_end": **********.999364, "duration": 0.010949850082397461, "duration_str": "10.95ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.234665, "relative_start": 2.****************, "end": **********.236472, "relative_end": **********.236472, "duration": 0.0018069744110107422, "duration_str": "1.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST public-bookings", "middleware": "web", "controller": "App\\Http\\Controllers\\BookingController@publicStore<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FBookingController.php&line=130\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "public-bookings.store", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FBookingController.php&line=130\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/BookingController.php:130-215</a>"}, "queries": {"count": 6, "nb_statements": 6, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.058949999999999995, "accumulated_duration_str": "58.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `calendar_events` where `id` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 984}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 955}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 686}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 481}], "start": **********.027595, "duration": 0.01426, "duration_str": "14.26ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "new_omx_saas", "explain": null, "start_percent": 0, "width_percent": 24.19}, {"sql": "select * from `calendar_events` where `calendar_events`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/BookingController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\BookingController.php", "line": 147}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.046078, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "BookingController.php:147", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/BookingController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\BookingController.php", "line": 147}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FBookingController.php&line=147", "ajax": false, "filename": "BookingController.php", "line": "147"}, "connection": "new_omx_saas", "explain": null, "start_percent": 24.19, "width_percent": 1.17}, {"sql": "insert into `bookings` (`event_id`, `name`, `email`, `phone`, `date`, `time`, `selected_location`, `custom_fields`, `custom_fields_value`, `payment_amount`, `payment_status`, `status`, `updated_at`, `created_at`) values (4, '<PERSON><PERSON>', '<EMAIL>', '6295298485', '2025-08-02', '12:30', '{\\\"type\\\":\\\"in_person\\\",\\\"value\\\":\\\"Siliguri\\\",\\\"display\\\":\\\"In-person meeting\\\"}', '[]', '[]', 500, 'pending', 'scheduled', '2025-08-02 01:23:03', '2025-08-02 01:23:03')", "type": "query", "params": [], "bindings": [4, "<PERSON><PERSON>", "<EMAIL>", "6295298485", "2025-08-02", "12:30", "{\"type\":\"in_person\",\"value\":\"Siliguri\",\"display\":\"In-person meeting\"}", "[]", "[]", 500, "pending", "scheduled", "2025-08-02 01:23:03", "2025-08-02 01:23:03"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/BookingController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\BookingController.php", "line": 150}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.05103, "duration": 0.0416, "duration_str": "41.6ms", "memory": 0, "memory_str": null, "filename": "BookingController.php:150", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/BookingController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\BookingController.php", "line": 150}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FBookingController.php&line=150", "ajax": false, "filename": "BookingController.php", "line": "150"}, "connection": "new_omx_saas", "explain": null, "start_percent": 25.36, "width_percent": 70.568}, {"sql": "select * from `users` where `id` = 74 limit 1", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 21, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 54}], "start": **********.12514, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "new_omx_saas", "explain": null, "start_percent": 95.929, "width_percent": 1.561}, {"sql": "select * from `users` where `users`.`id` = 74 limit 1", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 411}, {"index": 21, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 22, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/BookingController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\BookingController.php", "line": 179}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1313848, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "CrmWebhookDispatcher.php:411", "source": {"index": 20, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FServices%2FCrmWebhookDispatcher.php&line=411", "ajax": false, "filename": "CrmWebhookDispatcher.php", "line": "411"}, "connection": "new_omx_saas", "explain": null, "start_percent": 97.489, "width_percent": 1.34}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/ModuleWebhookService.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Services\\ModuleWebhookService.php", "line": 61}, {"index": 16, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 69}, {"index": 17, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 306}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/BookingController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\BookingController.php", "line": 179}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1369221, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ModuleWebhookService.php:61", "source": {"index": 15, "namespace": null, "name": "app/Services/ModuleWebhookService.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Services\\ModuleWebhookService.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FServices%2FModuleWebhookService.php&line=61", "ajax": false, "filename": "ModuleWebhookService.php", "line": "61"}, "connection": "new_omx_saas", "explain": null, "start_percent": 98.83, "width_percent": 1.17}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\CalendarEvent": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FCalendarEvent.php&line=1", "ajax": false, "filename": "CalendarEvent.php", "line": "?"}}, "App\\Models\\Booking": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FBooking.php&line=1", "ajax": false, "filename": "Booking.php", "line": "?"}}, "App\\Models\\ModuleIntegration": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FModuleIntegration.php&line=1", "ajax": false, "filename": "ModuleIntegration.php", "line": "?"}}}, "count": 5, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 4, "created": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QWSxIFjNu1s5OpxXmNdhn5W8ul114PqK4OYo41zx", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/calendar-events/4\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "74", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/public-bookings", "action_name": "public-bookings.store", "controller_action": "App\\Http\\Controllers\\BookingController@publicStore", "uri": "POST public-bookings", "controller": "App\\Http\\Controllers\\BookingController@publicStore<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FBookingController.php&line=130\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FBookingController.php&line=130\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/BookingController.php:130-215</a>", "middleware": "web", "duration": "2.88s", "peak_memory": "58MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-424857033 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-424857033\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-954274624 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>event_id</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Mintu Barman</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"25 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">6295298485</span>\"\n  \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-02</span>\"\n  \"<span class=sf-dump-key>time</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12:30</span>\"\n  \"<span class=sf-dump-key>selected_location</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">in_person</span>\"\n    \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Siliguri</span>\"\n    \"<span class=sf-dump-key>display</span>\" => \"<span class=sf-dump-str title=\"17 characters\">In-person meeting</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>timezone</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Asia/Calcutta</span>\"\n  \"<span class=sf-dump-key>custom_fields</span>\" => []\n  \"<span class=sf-dump-key>custom_fields_value</span>\" => []\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWSxIFjNu1s5OpxXmNdhn5W8ul114PqK4OYo41zx</span>\"\n  \"<span class=sf-dump-key>payment_amount</span>\" => <span class=sf-dump-num>500</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-954274624\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1197968568 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">362</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">QWSxIFjNu1s5OpxXmNdhn5W8ul114PqK4OYo41zx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/calendar-events/4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjZVREVBbC9XSnIwcnNtSE5nWGdkbWc9PSIsInZhbHVlIjoiSUZqRHlsTHl1NWFFNVYzbTdnRFhIdW5DUjY0RVFjV01mOXFNSnU5aFpBcnpsV3BWT21OZUwyM0pVMWxNT2VYZUlEa1A2WHU4cDYveHF6NVF3N1ZnV3lNY2pLM1BLUkJOMlZJZXJ0SUxnTjJJSC9BYmNKbHp1V1FlWGZNL3IweVM4Mi9jcUw4U3ZBYnZoY2JaRTkzbGo2L2pSUlVxMGk3b0wxUWZ2d2JkSUlwTDZnbUFsc3BRSGh2ZVRnZFBiV1o5aTlZcXlncElWVDA2VXdhei8wVEE2bXQzWkpUbSthTDI4eDNOaGtja0ZMVT0iLCJtYWMiOiJmYzk1YjdhMmFhYmZjYTM1ZjAxZWM4NTA5NWEyNWRmYjE1OWUxYjlkMTQ1NzUzMTY5NDM2YTk0Y2EyMDc5ZmI3IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6Im9La0FzZlE2YmtRMDlMT1VjMVRIcEE9PSIsInZhbHVlIjoiMUkvTE9PZ1hSUTUxUHBXcWtzZXlpWHQyenJmNEE4bkg1cDZMenFzbmZQUGkrbnNZWGRib2JpS2RZZVNGbWJRaXk1aFBUcTZ0bXlLMHkvWFQxL1lJK0RER2RjU0QrRFM5VE9uc211SlNnM1JKNllwTm9Nazhqbkk2MllKbzB1WkhROUQvYUhGS2tjbGhmb2ptdElSL2xhZlU2V0lNOGswVGJ6ZGhNM2JGbXNEWUVQUWZNYzdkcGd6bFdyL2ViU0JBcDJRb0R0NGRHeWtrUVEvV2N1NDZCZHBSdFJZSkR4Mmw4M01WVVQ4V0Y2UmZ2WGVTeHlhNWw3b3ZNam1XV3lEQU84Um0wQlZUQkhnbWlycVJnZVV4UXM5alVzbXpDV2NhcER6MjVRYXdRRmVlQlh1dncyaGV6NlZQZkJJSUhOVTdjbm9ZM2MyTm9PbFNVRmFLcVdXOGN6ZXBHeVppQ2JNZy9pKzhSVWkwRnRmZTk0dXFlckloaThzajk3b3B5bUEyT2tWUU1OR3NsK214czZCZkxTaWNsTWo0eTAzWXpFR0YyRE9aenFpbGtySFFyM1NzK2x0bDVvRGlmbnRObkxodlpPeDhsTVVWSFoyUG1MWEdmd08zMSsrbWZCUW01TjNFd0hBVk12b3hUMk5RdzNVSUN0V0pvQVdWTGtnaWRaZE0iLCJtYWMiOiJjOGNkMDY1OWZjMjQxNjU3MWQ4YmI1NDdjNGQzY2ZjMTE0Nzc2ZTg2NWYxNWYzNzc2YThmYjVmODg4YTg2ODg4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ind5RHFiL0E3MStva29TaCtUZ2RqQ1E9PSIsInZhbHVlIjoiR0VnclRzMG1LMitXOHNCREhyZjBzZGRHa1llOGw5RzZra1d1NkdnTEFOTy9peWFGUXcwV2FjMk9Za1dEeEgzWHhPVGUxbytzU1gxQlZrQVBCK0hUMGdIK2JOZVRxd2o2NFVRV1hjSXJsK0hYeTZpYklmOXM0WVhwMTlJVW9MTHdCWkcvVVVvamJ6aDNZbHNhcEZINUM3Nk4xbXhWbDVVK1pGL3hudFR1TVB3RFl5TUE3NVpBa0xMWHNUV21RZG1URDFzbEhlemhmTWIvV2xHdTlMWmU4dUxBd0pOQzFTQUd3TGh6bkhPbEgwRDVMWGJYSzk4S3QwVEVUZHNJYk90WTE3aEhyWGtLQWRmYmRweTVSdUxnNUl1czdRQmNDbUxwZ21CVTZFa3pjSStZTjN0UG84bnJVbzEzKy9sVFhqZ1NtcnI3Qk5oQlRNRTRoeFlmdE1yU1p4TXJpTjVwZW9abThsYnlaZTFkMXlsMXpnOHVMZncvYXZFVm9lN1hpa0hYd1JtcnNxZXRZV1B0K1h4alpxaGR3NVZhM2lCVU04eGhpTXZ6RmpiSW5XQ0taZmY0MU5OMkMxOUVYK3VlclhMZ3hicS9xekl3a1BxbnQ4bmV6R2Fib2g4YjJKVkpodC93WFEzNHJUNXpCbHZSOUFBSzhMNERIVTdhSHl3aHZqSGciLCJtYWMiOiJjMzM2YmM3MjNjOWJkNzVlM2Q2MGFiM2RlNmU1MWJhNzM5YzA4MTcwM2EyNjUzMWQwMmE4OWNiNjFjMjgwMjYxIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IklINTYrMVB5bEtXNVZ3VUkyY3ZiREE9PSIsInZhbHVlIjoiNnB6Yk12aXg4bmVoaVhuN3RIRzdGTUdXVVF6d0V1eTc3MXFnUm9CRGtmeW1LRnVFYnNvdmsvem9Xb1B5R3ZHMWY3M0NCbWJqc0Z4Qmp0L0lVdGUydmMwVTBIVWtxVzRxQ05RWVRDZWFIV1hyYmRYU05qbXhtUDBnWkRYWFBxOVQ0NVhqaHhJWDQ0c1BFMFhBM0VFdFhwdHNYRUtLaGdqZXlQbXVseEowdHhkT2hORVNoRlE4M1kybktRS2V6WmhubEtnTjlWZFpaaGJQTEE5R1Y4YmRZOHd3dkRMVnRmNmN2YjlVTE5Gb2hEOHBnUTZZMVNiRkgvZDk0dFZlbXZCLzNWeklJNWt2ZjJnWjRJTTQ3a3ZRRGZudnUwZU42ZEViR2E0ZjhGMzNjVXhnK0pHQ0xsVmVyS1F0VTg1S2tIRFMxdHdhWUtBTlQyNDN2c2ZGdUQvNlFTVEtreXBtVzJ3RjQvWVlxSFZFK2dvQ1pDRDRFTERGNklDMFloemU3Nm4xNHRKQVd5aTNqc2dRbzJGdXZ6bGd2ejVadTJhY1FhSDJ4SnBYMHVURHVjclNTS01ja1ZETWplNXBJVTNwZ2pwS0pLM1dSTXpjQ0VWRS9VVlRQOXVBaTZmRHE4MEd1bW9leTdXSkwrd2xUcjJhSjVRdzlGRGVaR2FmWXJyWE1sZkUiLCJtYWMiOiI3MjllMjcwMTY3Nzk3NGI2YTkzZWZiMGUyODdiZGY2Mjc2ODVlYTQ3NzVlMjkzMzI3Y2MzZDUyNjU1MmVhMGUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1197968568\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-979857079 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NnOpBkKlzDCYP0gC0gA26hvqmORlKbmYh1U3VF2B</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWSxIFjNu1s5OpxXmNdhn5W8ul114PqK4OYo41zx</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0aKMqKkrxmyGXbPQ7Du4TVovUvHLOwr5Gqmkro7o</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-979857079\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-229320375 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 01:23:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-229320375\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-134369906 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QWSxIFjNu1s5OpxXmNdhn5W8ul114PqK4OYo41zx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/calendar-events/4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-134369906\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/public-bookings", "action_name": "public-bookings.store", "controller_action": "App\\Http\\Controllers\\BookingController@publicStore"}, "badge": null}}